"""
设置管理类 - 处理应用程序配置和设置
Settings Manager - Handle application configuration and settings
"""

import os
import json
import datetime
from utils.file_utils import FileManager

class SettingsManager:
    """设置管理器类"""
    
    def __init__(self):
        """初始化设置管理器"""
        self.file_manager = FileManager()
        self.config_file = self.file_manager.get_config_file_path("config.json")
        self.history_file = self.file_manager.get_config_file_path("history.json")
        self.commands_file = self.file_manager.get_config_file_path("commands.json")
        
        # 默认设置
        self.default_settings = {
            "theme": "light",
            "default_terminal": "default",
            "last_path": os.getcwd(),
            "window_geometry": "1000x700",
            "auto_save": True,
            "max_history_entries": 1000,
            "language": "zh_CN"
        }
        
        # 默认命令分类
        self.default_commands = [
            {
                "name": "Git命令",
                "description": "常用Git操作命令",
                "commands": [
                    {"name": "Git状态", "command": "git status", "description": "查看Git仓库状态"},
                    {"name": "Git日志", "command": "git log --oneline -10", "description": "查看最近10条提交记录"},
                    {"name": "Git分支", "command": "git branch -a", "description": "查看所有分支"},
                    {"name": "拉取更新", "command": "git pull", "description": "拉取远程更新"},
                    {"name": "推送代码", "command": "git push", "description": "推送代码到远程仓库"}
                ]
            },
            {
                "name": "系统命令",
                "description": "常用系统操作命令",
                "commands": [
                    {"name": "列出文件", "command": "dir" if os.name == 'nt' else "ls -la", "description": "列出当前目录文件"},
                    {"name": "当前路径", "command": "cd" if os.name == 'nt' else "pwd", "description": "显示当前路径"},
                    {"name": "磁盘使用", "command": "dir /-c" if os.name == 'nt' else "df -h", "description": "查看磁盘使用情况"},
                    {"name": "进程列表", "command": "tasklist" if os.name == 'nt' else "ps aux", "description": "查看运行进程"}
                ]
            },
            {
                "name": "开发工具",
                "description": "开发相关命令",
                "commands": [
                    {"name": "Python版本", "command": "python --version", "description": "查看Python版本"},
                    {"name": "Node版本", "command": "node --version", "description": "查看Node.js版本"},
                    {"name": "NPM版本", "command": "npm --version", "description": "查看NPM版本"},
                    {"name": "安装依赖", "command": "npm install", "description": "安装NPM依赖"},
                    {"name": "运行项目", "command": "npm start", "description": "启动项目"}
                ]
            }
        ]
        
        # 加载设置
        self.settings = self.load_settings()
        self.history = self.load_history()
        self.command_categories = self.load_commands()
    
    def load_settings(self):
        """加载设置"""
        settings = self.file_manager.read_json_file(self.config_file, self.default_settings.copy())
        
        # 确保所有默认设置都存在
        for key, value in self.default_settings.items():
            if key not in settings:
                settings[key] = value
        
        return settings
    
    def save_settings(self):
        """保存设置"""
        return self.file_manager.write_json_file(self.config_file, self.settings)
    
    def get_setting(self, key, default=None):
        """获取设置值"""
        return self.settings.get(key, default)
    
    def set_setting(self, key, value):
        """设置值"""
        self.settings[key] = value
        if self.settings.get("auto_save", True):
            self.save_settings()
    
    def get_theme(self):
        """获取主题"""
        return self.get_setting("theme", "light")
    
    def set_theme(self, theme):
        """设置主题"""
        self.set_setting("theme", theme)
    
    def get_default_terminal(self):
        """获取默认终端"""
        return self.get_setting("default_terminal", "default")
    
    def set_default_terminal(self, terminal):
        """设置默认终端"""
        self.set_setting("default_terminal", terminal)
    
    def get_last_path(self):
        """获取上次使用的路径"""
        return self.get_setting("last_path", os.getcwd())
    
    def set_last_path(self, path):
        """设置上次使用的路径"""
        self.set_setting("last_path", path)
    
    def get_window_geometry(self):
        """获取窗口几何信息"""
        return self.get_setting("window_geometry", "1000x700")
    
    def set_window_geometry(self, geometry):
        """设置窗口几何信息"""
        self.set_setting("window_geometry", geometry)
    
    def load_history(self):
        """加载历史记录"""
        history_data = self.file_manager.read_json_file(self.history_file, {"entries": []})
        return history_data.get("entries", [])
    
    def save_history(self):
        """保存历史记录"""
        history_data = {"entries": self.history}
        return self.file_manager.write_json_file(self.history_file, history_data)
    
    def add_history_entry(self, message):
        """添加历史记录条目"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        entry = f"[{timestamp}] {message}"
        
        self.history.append(entry)
        
        # 限制历史记录数量
        max_entries = self.get_setting("max_history_entries", 1000)
        if len(self.history) > max_entries:
            self.history = self.history[-max_entries:]
        
        if self.settings.get("auto_save", True):
            self.save_history()
    
    def get_history(self):
        """获取历史记录"""
        return self.history.copy()
    
    def clear_history(self):
        """清空历史记录"""
        self.history = []
        self.save_history()
    
    def load_commands(self):
        """加载命令分类"""
        commands = self.file_manager.read_json_file(self.commands_file)

        # 如果没有保存的命令，使用默认命令
        if not commands or not commands.get("categories"):
            commands = {"categories": self.default_commands.copy()}
            # 直接保存，避免循环依赖
            self.file_manager.write_json_file(self.commands_file, commands)

        return commands.get("categories", [])
    
    def save_commands(self):
        """保存命令分类"""
        commands_data = {"categories": self.command_categories}
        return self.file_manager.write_json_file(self.commands_file, commands_data)

    def get_command_categories(self):
        """获取命令分类"""
        return self.command_categories.copy()
    
    def add_command_category(self, name, description=""):
        """添加命令分类"""
        category = {
            "name": name,
            "description": description,
            "commands": []
        }
        self.command_categories.append(category)
        
        if self.settings.get("auto_save", True):
            self.save_commands()
        
        return category
    
    def remove_command_category(self, name):
        """删除命令分类"""
        self.command_categories = [cat for cat in self.command_categories if cat["name"] != name]
        
        if self.settings.get("auto_save", True):
            self.save_commands()
    
    def update_command_category(self, old_name, new_name, description=""):
        """更新命令分类"""
        for category in self.command_categories:
            if category["name"] == old_name:
                category["name"] = new_name
                category["description"] = description
                break
        
        if self.settings.get("auto_save", True):
            self.save_commands()
    
    def add_command(self, category_name, command_name, command, description=""):
        """添加命令到分类"""
        for category in self.command_categories:
            if category["name"] == category_name:
                command_obj = {
                    "name": command_name,
                    "command": command,
                    "description": description
                }
                category["commands"].append(command_obj)
                
                if self.settings.get("auto_save", True):
                    self.save_commands()
                
                return command_obj
        
        return None
    
    def remove_command(self, category_name, command_name):
        """从分类中删除命令"""
        for category in self.command_categories:
            if category["name"] == category_name:
                category["commands"] = [
                    cmd for cmd in category["commands"] 
                    if cmd["name"] != command_name
                ]
                
                if self.settings.get("auto_save", True):
                    self.save_commands()
                
                break
    
    def update_command(self, category_name, old_command_name, new_command_name, command, description=""):
        """更新命令"""
        for category in self.command_categories:
            if category["name"] == category_name:
                for cmd in category["commands"]:
                    if cmd["name"] == old_command_name:
                        cmd["name"] = new_command_name
                        cmd["command"] = command
                        cmd["description"] = description
                        
                        if self.settings.get("auto_save", True):
                            self.save_commands()
                        
                        return cmd
        
        return None
    
    def export_settings(self, filepath):
        """导出设置"""
        export_data = {
            "settings": self.settings,
            "commands": self.command_categories,
            "export_time": datetime.datetime.now().isoformat()
        }
        
        return self.file_manager.write_json_file(filepath, export_data)
    
    def import_settings(self, filepath):
        """导入设置"""
        try:
            import_data = self.file_manager.read_json_file(filepath)
            
            if "settings" in import_data:
                # 合并设置，保留当前设置中的重要值
                for key, value in import_data["settings"].items():
                    if key not in ["window_geometry"]:  # 不导入窗口几何信息
                        self.settings[key] = value
            
            if "commands" in import_data:
                self.command_categories = import_data["commands"]
            
            # 保存导入的设置
            self.save_settings()
            self.save_commands()
            
            return True
        except Exception as e:
            print(f"导入设置失败: {e}")
            return False
    
    def reset_to_defaults(self):
        """重置为默认设置"""
        self.settings = self.default_settings.copy()
        self.command_categories = self.default_commands.copy()
        
        self.save_settings()
        self.save_commands()
    
    def save(self):
        """保存所有设置"""
        success = True
        success &= self.save_settings()
        success &= self.save_commands()
        success &= self.save_history()
        return success
