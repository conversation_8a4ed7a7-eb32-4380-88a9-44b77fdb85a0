{"version": 3, "file": "ExplorerBase.js", "sourceRoot": "", "sources": ["../src/ExplorerBase.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAwB;AASxB,uCAA8C;AAE9C;;GAEG;AACH,MAAsB,YAAY;IAGhC,kBAAkB,GAAG,KAAK,CAAC;IAER,MAAM,CAAI;IACV,SAAS,CAEb;IACI,WAAW,CAEf;IAEf,YAAmB,OAAoB;QACrC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED,IAAc,iBAAiB,CAAC,KAAc;QAC5C,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;IAClC,CAAC;IAED,eAAe;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,YAAY,EAAE;YACvC,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtC,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,MAAM,IAAI,KAAK,CACb,sBAAsB,uBAAuB,CAAC,KAAK,CAAC,GAAG,CACxD,CAAC;aACH;YACD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAChC,MAAM,IAAI,KAAK,CACb,cAAc,uBAAuB,CACnC,KAAK,CACN,gCAAgC,OAAO,MAAM,GAAG,CAClD,CAAC;aACH;SACF;IACH,CAAC;IAEM,cAAc;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;SACxB;IACH,CAAC;IAEM,gBAAgB;QACrB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;SAC1B;IACH,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAES,mBAAmB,CAC3B,QAAgB,EAChB,MAAc;QAEd,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QACD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACvD;QACD,IACE,IAAI,CAAC,MAAM,CAAC,uCAAuC;YACnD,IAAI,CAAC,kBAAkB,EACvB;YACA,MAAM,GAAG,IAAA,2BAAiB,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;SAC7D;QACD,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SACvD;QACD,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;IAC9B,CAAC;CACF;AAvFD,oCAuFC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,SAAkB;IACxD,uCAAuC;IACvC,OAAO,SAAS,CAAC,CAAC,CAAC,cAAc,SAAS,GAAG,CAAC,CAAC,CAAC,0BAA0B,CAAC;AAC7E,CAAC;AAHD,0DAGC"}