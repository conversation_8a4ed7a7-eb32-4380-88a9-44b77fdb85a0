#!/usr/bin/env python3
"""
命令执行测试脚本
Test script for command execution functionality
"""

import os
import sys
from utils.terminal_utils import Terminal<PERSON>auncher

def test_command_execution():
    """测试命令执行功能"""
    print("=== 命令执行测试 ===")
    
    launcher = TerminalLauncher()
    
    # 获取当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 获取可用终端列表
    available_terminals = launcher.get_available_terminals()
    print(f"可用终端: {available_terminals}")
    
    if available_terminals:
        # 测试在终端中执行命令
        terminal_type = available_terminals[0]
        test_command = "dir" if os.name == 'nt' else "ls -la"
        
        print(f"尝试在 {terminal_type} 中执行命令: {test_command}")
        
        try:
            success = launcher.launch_terminal(current_dir, terminal_type, test_command)
            if success:
                print("✅ 命令执行成功! 请检查新打开的终端窗口")
            else:
                print("❌ 命令执行失败!")
        except Exception as e:
            print(f"❌ 执行命令时发生错误: {e}")
    else:
        print("❌ 没有找到可用的终端")

if __name__ == "__main__":
    test_command_execution()
