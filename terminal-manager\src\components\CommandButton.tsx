import React, { useState } from 'react';
import { useTerminal } from '../contexts/TerminalContext';
import { Command } from '../contexts/CommandContext';

interface CommandButtonProps {
  command: Command;
  disabled?: boolean;
}

const CommandButton: React.FC<CommandButtonProps> = ({ command, disabled = false }) => {
  const { executeCommand } = useTerminal();
  const [isExecuting, setIsExecuting] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const handleExecute = async () => {
    if (disabled || isExecuting) return;
    
    setIsExecuting(true);
    setShowConfirm(false);
    
    try {
      await executeCommand(command.command);
    } catch (error) {
      console.error('命令执行失败:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  const handleCancel = () => {
    setShowConfirm(false);
  };

  const handleClick = () => {
    setShowConfirm(true);
  };

  return (
    <div className="command-button-container">
      <div className="command-info">
        <h4 className="command-name">{command.name}</h4>
        <code className="command-text">{command.command}</code>
      </div>
      
      <div className="command-actions">
        {!showConfirm ? (
          <button
            onClick={handleClick}
            disabled={disabled || isExecuting}
            className="command-button"
          >
            {isExecuting ? '执行中...' : '执行'}
          </button>
        ) : (
          <div className="confirm-actions">
            <button
              onClick={handleExecute}
              disabled={isExecuting}
              className="confirm-button"
            >
              {isExecuting ? '执行中...' : '发送'}
            </button>
            <button
              onClick={handleCancel}
              disabled={isExecuting}
              className="cancel-button"
            >
              取消
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommandButton;
