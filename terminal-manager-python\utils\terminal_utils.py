"""
终端工具类 - 处理终端启动和管理
Terminal Utilities - Handle terminal launching and management
"""

import os
import subprocess
import platform
import sys

class TerminalLauncher:
    """终端启动器类"""
    
    def __init__(self):
        """初始化终端启动器"""
        self.system = platform.system()
        self.default_terminals = self._get_default_terminals()
    
    def _get_default_terminals(self):
        """获取系统默认终端列表"""
        if self.system == "Windows":
            return {
                "cmd": self._launch_cmd,
                "powershell": self._launch_powershell,
                "wt": self._launch_windows_terminal
            }
        elif self.system == "Darwin":  # macOS
            return {
                "Terminal": self._launch_macos_terminal,
                "iTerm": self._launch_iterm
            }
        else:  # Linux
            return {
                "gnome-terminal": self._launch_gnome_terminal,
                "xterm": self._launch_xterm,
                "konsole": self._launch_konsole
            }
    
    def launch_terminal(self, path, terminal_type="default"):
        """
        启动终端
        
        Args:
            path (str): 工作目录路径
            terminal_type (str): 终端类型
            
        Returns:
            bool: 启动是否成功
        """
        if not os.path.exists(path):
            raise ValueError(f"路径不存在: {path}")
        
        if terminal_type == "default":
            terminal_type = self._get_default_terminal_type()
        
        launcher = self.default_terminals.get(terminal_type)
        if not launcher:
            raise ValueError(f"不支持的终端类型: {terminal_type}")
        
        try:
            return launcher(path)
        except Exception as e:
            print(f"启动终端失败: {e}")
            return False
    
    def _get_default_terminal_type(self):
        """获取默认终端类型"""
        if self.system == "Windows":
            # 优先使用Windows Terminal，然后是PowerShell，最后是CMD
            if self._is_windows_terminal_available():
                return "wt"
            elif self._is_powershell_available():
                return "powershell"
            else:
                return "cmd"
        elif self.system == "Darwin":
            return "Terminal"
        else:
            # Linux - 尝试常见的终端
            for terminal in ["gnome-terminal", "xterm", "konsole"]:
                if self._is_command_available(terminal):
                    return terminal
            return "xterm"  # 最后的备选
    
    def _is_windows_terminal_available(self):
        """检查Windows Terminal是否可用"""
        try:
            subprocess.run(["wt", "--help"], 
                         capture_output=True, 
                         check=True, 
                         timeout=5)
            return True
        except:
            return False
    
    def _is_powershell_available(self):
        """检查PowerShell是否可用"""
        try:
            subprocess.run(["powershell", "-Command", "Get-Host"], 
                         capture_output=True, 
                         check=True, 
                         timeout=5)
            return True
        except:
            return False
    
    def _is_command_available(self, command):
        """检查命令是否可用"""
        try:
            subprocess.run([command, "--version"], 
                         capture_output=True, 
                         timeout=5)
            return True
        except:
            return False
    
    # Windows终端启动方法
    def _launch_cmd(self, path):
        """启动CMD"""
        try:
            subprocess.Popen(
                ["cmd", "/k", f"cd /d \"{path}\""],
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            return True
        except Exception as e:
            print(f"启动CMD失败: {e}")
            return False
    
    def _launch_powershell(self, path):
        """启动PowerShell"""
        try:
            subprocess.Popen(
                ["powershell", "-NoExit", "-Command", f"Set-Location '{path}'"],
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            return True
        except Exception as e:
            print(f"启动PowerShell失败: {e}")
            return False
    
    def _launch_windows_terminal(self, path):
        """启动Windows Terminal"""
        try:
            subprocess.Popen(
                ["wt", "-d", path],
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            return True
        except Exception as e:
            print(f"启动Windows Terminal失败: {e}")
            return False
    
    # macOS终端启动方法
    def _launch_macos_terminal(self, path):
        """启动macOS Terminal"""
        try:
            script = f'''
            tell application "Terminal"
                activate
                do script "cd '{path}'"
            end tell
            '''
            subprocess.Popen(["osascript", "-e", script])
            return True
        except Exception as e:
            print(f"启动Terminal失败: {e}")
            return False
    
    def _launch_iterm(self, path):
        """启动iTerm"""
        try:
            script = f'''
            tell application "iTerm"
                activate
                create window with default profile
                tell current session of current window
                    write text "cd '{path}'"
                end tell
            end tell
            '''
            subprocess.Popen(["osascript", "-e", script])
            return True
        except Exception as e:
            print(f"启动iTerm失败: {e}")
            return False
    
    # Linux终端启动方法
    def _launch_gnome_terminal(self, path):
        """启动GNOME Terminal"""
        try:
            subprocess.Popen(
                ["gnome-terminal", "--working-directory", path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            print(f"启动GNOME Terminal失败: {e}")
            return False
    
    def _launch_xterm(self, path):
        """启动XTerm"""
        try:
            # 创建一个启动脚本来设置工作目录
            script = f"cd '{path}' && exec $SHELL"
            subprocess.Popen(
                ["xterm", "-e", "bash", "-c", script],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            print(f"启动XTerm失败: {e}")
            return False
    
    def _launch_konsole(self, path):
        """启动Konsole"""
        try:
            subprocess.Popen(
                ["konsole", "--workdir", path],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
            return True
        except Exception as e:
            print(f"启动Konsole失败: {e}")
            return False
    
    def get_available_terminals(self):
        """获取可用的终端列表"""
        available = []
        for terminal_type, launcher in self.default_terminals.items():
            try:
                # 简单测试终端是否可用
                if self.system == "Windows":
                    if terminal_type == "cmd":
                        available.append(terminal_type)
                    elif terminal_type == "powershell" and self._is_powershell_available():
                        available.append(terminal_type)
                    elif terminal_type == "wt" and self._is_windows_terminal_available():
                        available.append(terminal_type)
                else:
                    if self._is_command_available(terminal_type):
                        available.append(terminal_type)
            except:
                continue
        
        return available

class CommandExecutor:
    """命令执行器类"""
    
    def __init__(self):
        """初始化命令执行器"""
        pass
    
    def execute_command(self, command, working_dir=None, timeout=30):
        """
        执行命令
        
        Args:
            command (str): 要执行的命令
            working_dir (str): 工作目录
            timeout (int): 超时时间（秒）
            
        Returns:
            dict: 执行结果
        """
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            return {
                "success": True,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "命令执行超时",
                "timeout": True
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timeout": False
            }
    
    def execute_command_async(self, command, working_dir=None):
        """
        异步执行命令
        
        Args:
            command (str): 要执行的命令
            working_dir (str): 工作目录
            
        Returns:
            subprocess.Popen: 进程对象
        """
        try:
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=working_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            return process
        except Exception as e:
            print(f"异步执行命令失败: {e}")
            return None
