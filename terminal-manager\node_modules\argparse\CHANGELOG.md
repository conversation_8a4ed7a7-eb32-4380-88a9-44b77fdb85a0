# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).


## [2.0.1] - 2020-08-29
### Fixed
- Fix issue with `process.argv` when used with interpreters (`coffee`, `ts-node`, etc.), #150.


## [2.0.0] - 2020-08-14
### Changed
- Full rewrite. Now port from python 3.9.0 & more precise following.
  See [doc](./doc) for difference and migration info.
- node.js 10+ required
- Removed most of local docs in favour of original ones.


## [1.0.10] - 2018-02-15
### Fixed
- Use .concat instead of + for arrays, #122.


## [1.0.9] - 2016-09-29
### Changed
- Rerelease after 1.0.8 - deps cleanup.


## [1.0.8] - 2016-09-29
### Changed
- Maintenance (deps bump, fix node 6.5+ tests, coverage report).


## [1.0.7] - 2016-03-17
### Changed
- Teach `addArgument` to accept string arg names. #97, @tomxtobin.


## [1.0.6] - 2016-02-06
### Changed
- Maintenance: moved to eslint & updated CS.


## [1.0.5] - 2016-02-05
### Changed
- Removed lodash dependency to significantly reduce install size.
  Thanks to @mourner.


## [1.0.4] - 2016-01-17
### Changed
- Maintenance: lodash update to 4.0.0.


## [1.0.3] - 2015-10-27
### Fixed
- Fix parse `=` in args: `--examplepath="C:\myfolder\env=x64"`. #84, @CatWithApple.


## [1.0.2] - 2015-03-22
### Changed
- Relaxed lodash version dependency.


## [1.0.1] - 2015-02-20
### Changed
- Changed dependencies to be compatible with ancient nodejs.


## [1.0.0] - 2015-02-19
### Changed
- Maintenance release.
- Replaced `underscore` with `lodash`.
- Bumped version to 1.0.0 to better reflect semver meaning.
- HISTORY.md -> CHANGELOG.md


## [0.1.16] - 2013-12-01
### Changed
- Maintenance release. Updated dependencies and docs.


## [0.1.15] - 2013-05-13
### Fixed
- Fixed #55, @trebor89


## [0.1.14] - 2013-05-12
### Fixed
- Fixed #62, @maxtaco


## [0.1.13] - 2013-04-08
### Changed
- Added `.npmignore` to reduce package size


## [0.1.12] - 2013-02-10
### Fixed
- Fixed conflictHandler (#46), @hpaulj


## [0.1.11] - 2013-02-07
### Added
- Added 70+ tests (ported from python), @hpaulj
- Added conflictHandler, @applepicke
- Added fromfilePrefixChar, @hpaulj

### Fixed
- Multiple bugfixes, @hpaulj


## [0.1.10] - 2012-12-30
### Added
- Added [mutual exclusion](http://docs.python.org/dev/library/argparse.html#mutual-exclusion)
  support, thanks to @hpaulj

### Fixed
- Fixed options check for `storeConst` & `appendConst` actions, thanks to @hpaulj


## [0.1.9] - 2012-12-27
### Fixed
- Fixed option dest interferens with other options (issue #23), thanks to @hpaulj
- Fixed default value behavior with `*` positionals, thanks to @hpaulj
- Improve `getDefault()` behavior, thanks to @hpaulj
- Improve negative argument parsing, thanks to @hpaulj


## [0.1.8] - 2012-12-01
### Fixed
- Fixed parser parents (issue #19), thanks to @hpaulj
- Fixed negative argument parse (issue #20), thanks to @hpaulj


## [0.1.7] - 2012-10-14
### Fixed
- Fixed 'choices' argument parse (issue #16)
- Fixed stderr output (issue #15)


## [0.1.6] - 2012-09-09
### Fixed
- Fixed check for conflict of options (thanks to @tomxtobin)


## [0.1.5] - 2012-09-03
### Fixed
- Fix parser #setDefaults method (thanks to @tomxtobin)


## [0.1.4] - 2012-07-30
### Fixed
- Fixed pseudo-argument support (thanks to @CGamesPlay)
- Fixed addHelp default (should be true), if not set (thanks to @benblank)


## [0.1.3] - 2012-06-27
### Fixed
- Fixed formatter api name: Formatter -> HelpFormatter


## [0.1.2] - 2012-05-29
### Fixed
- Removed excess whitespace in help
- Fixed error reporting, when parcer with subcommands
  called with empty arguments

### Added
- Added basic tests


## [0.1.1] - 2012-05-23
### Fixed
- Fixed line wrapping in help formatter
- Added better error reporting on invalid arguments


## [0.1.0] - 2012-05-16
### Added
- First release.


[2.0.1]: https://github.com/nodeca/argparse/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/nodeca/argparse/compare/1.0.10...2.0.0
[1.0.10]: https://github.com/nodeca/argparse/compare/1.0.9...1.0.10
[1.0.9]: https://github.com/nodeca/argparse/compare/1.0.8...1.0.9
[1.0.8]: https://github.com/nodeca/argparse/compare/1.0.7...1.0.8
[1.0.7]: https://github.com/nodeca/argparse/compare/1.0.6...1.0.7
[1.0.6]: https://github.com/nodeca/argparse/compare/1.0.5...1.0.6
[1.0.5]: https://github.com/nodeca/argparse/compare/1.0.4...1.0.5
[1.0.4]: https://github.com/nodeca/argparse/compare/1.0.3...1.0.4
[1.0.3]: https://github.com/nodeca/argparse/compare/1.0.2...1.0.3
[1.0.2]: https://github.com/nodeca/argparse/compare/1.0.1...1.0.2
[1.0.1]: https://github.com/nodeca/argparse/compare/1.0.0...1.0.1
[1.0.0]: https://github.com/nodeca/argparse/compare/0.1.16...1.0.0
[0.1.16]: https://github.com/nodeca/argparse/compare/0.1.15...0.1.16
[0.1.15]: https://github.com/nodeca/argparse/compare/0.1.14...0.1.15
[0.1.14]: https://github.com/nodeca/argparse/compare/0.1.13...0.1.14
[0.1.13]: https://github.com/nodeca/argparse/compare/0.1.12...0.1.13
[0.1.12]: https://github.com/nodeca/argparse/compare/0.1.11...0.1.12
[0.1.11]: https://github.com/nodeca/argparse/compare/0.1.10...0.1.11
[0.1.10]: https://github.com/nodeca/argparse/compare/0.1.9...0.1.10
[0.1.9]: https://github.com/nodeca/argparse/compare/0.1.8...0.1.9
[0.1.8]: https://github.com/nodeca/argparse/compare/0.1.7...0.1.8
[0.1.7]: https://github.com/nodeca/argparse/compare/0.1.6...0.1.7
[0.1.6]: https://github.com/nodeca/argparse/compare/0.1.5...0.1.6
[0.1.5]: https://github.com/nodeca/argparse/compare/0.1.4...0.1.5
[0.1.4]: https://github.com/nodeca/argparse/compare/0.1.3...0.1.4
[0.1.3]: https://github.com/nodeca/argparse/compare/0.1.2...0.1.3
[0.1.2]: https://github.com/nodeca/argparse/compare/0.1.1...0.1.2
[0.1.1]: https://github.com/nodeca/argparse/compare/0.1.0...0.1.1
[0.1.0]: https://github.com/nodeca/argparse/releases/tag/0.1.0
