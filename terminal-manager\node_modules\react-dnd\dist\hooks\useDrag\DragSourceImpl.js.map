{"version": 3, "sources": ["../../../src/hooks/useDrag/DragSourceImpl.ts"], "sourcesContent": ["import type { DragDropMonitor, DragSource, Identifier } from 'dnd-core'\n\nimport type { Connector } from '../../internals/index.js'\nimport type { DragSourceMonitor } from '../../types/index.js'\nimport type { DragObjectFactory, DragSourceHookSpec } from '../types.js'\n\nexport class DragSourceImpl<O, R, P> implements DragSource {\n\tpublic constructor(\n\t\tpublic spec: DragSourceHookSpec<O, R, P>,\n\t\tprivate monitor: DragSourceMonitor<O, R>,\n\t\tprivate connector: Connector,\n\t) {}\n\n\tpublic beginDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\n\t\tlet result: O | null = null\n\t\tif (typeof spec.item === 'object') {\n\t\t\tresult = spec.item as O\n\t\t} else if (typeof spec.item === 'function') {\n\t\t\tresult = (spec.item as DragObjectFactory<O>)(monitor)\n\t\t} else {\n\t\t\tresult = {} as O\n\t\t}\n\t\treturn result ?? null\n\t}\n\n\tpublic canDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tif (typeof spec.canDrag === 'boolean') {\n\t\t\treturn spec.canDrag\n\t\t} else if (typeof spec.canDrag === 'function') {\n\t\t\treturn spec.canDrag(monitor)\n\t\t} else {\n\t\t\treturn true\n\t\t}\n\t}\n\n\tpublic isDragging(globalMonitor: DragDropMonitor, target: Identifier) {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst { isDragging } = spec\n\t\treturn isDragging\n\t\t\t? isDragging(monitor)\n\t\t\t: target === globalMonitor.getSourceId()\n\t}\n\n\tpublic endDrag() {\n\t\tconst spec = this.spec\n\t\tconst monitor = this.monitor\n\t\tconst connector = this.connector\n\t\tconst { end } = spec\n\t\tif (end) {\n\t\t\tend(monitor.getItem(), monitor)\n\t\t}\n\t\tconnector.reconnect()\n\t}\n}\n"], "names": ["DragSourceImpl", "beginDrag", "spec", "monitor", "result", "item", "canDrag", "isDragging", "globalMonitor", "target", "getSourceId", "endDrag", "connector", "end", "getItem", "reconnect"], "mappings": "AAMA,OAAO,MAAMA,cAAc;IAO1B,AAAOC,SAAS,GAAG;QAClB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAE5B,IAAIC,MAAM,GAAa,IAAI;QAC3B,IAAI,OAAOF,IAAI,CAACG,IAAI,KAAK,QAAQ,EAAE;YAClCD,MAAM,GAAGF,IAAI,CAACG,IAAI,AAAK;SACvB,MAAM,IAAI,OAAOH,IAAI,CAACG,IAAI,KAAK,UAAU,EAAE;YAC3CD,MAAM,GAAG,AAACF,IAAI,CAACG,IAAI,CAA0BF,OAAO,CAAC;SACrD,MAAM;YACNC,MAAM,GAAG,EAAE,AAAK;SAChB;QACD,OAAOA,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,IAAI,CAAA;KACrB;IAED,AAAOE,OAAO,GAAG;QAChB,MAAMJ,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,IAAI,OAAOD,IAAI,CAACI,OAAO,KAAK,SAAS,EAAE;YACtC,OAAOJ,IAAI,CAACI,OAAO,CAAA;SACnB,MAAM,IAAI,OAAOJ,IAAI,CAACI,OAAO,KAAK,UAAU,EAAE;YAC9C,OAAOJ,IAAI,CAACI,OAAO,CAACH,OAAO,CAAC,CAAA;SAC5B,MAAM;YACN,OAAO,IAAI,CAAA;SACX;KACD;IAED,AAAOI,UAAU,CAACC,aAA8B,EAAEC,MAAkB,EAAE;QACrE,MAAMP,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,MAAM,EAAEI,UAAU,CAAA,EAAE,GAAGL,IAAI;QAC3B,OAAOK,UAAU,GACdA,UAAU,CAACJ,OAAO,CAAC,GACnBM,MAAM,KAAKD,aAAa,CAACE,WAAW,EAAE,CAAA;KACzC;IAED,AAAOC,OAAO,GAAG;QAChB,MAAMT,IAAI,GAAG,IAAI,CAACA,IAAI;QACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;QAC5B,MAAMS,SAAS,GAAG,IAAI,CAACA,SAAS;QAChC,MAAM,EAAEC,GAAG,CAAA,EAAE,GAAGX,IAAI;QACpB,IAAIW,GAAG,EAAE;YACRA,GAAG,CAACV,OAAO,CAACW,OAAO,EAAE,EAAEX,OAAO,CAAC;SAC/B;QACDS,SAAS,CAACG,SAAS,EAAE;KACrB;IAnDD,YACQb,IAAiC,EAChCC,OAAgC,EAChCS,SAAoB,CAC3B;aAHMV,IAAiC,GAAjCA,IAAiC;aAChCC,OAAgC,GAAhCA,OAAgC;aAChCS,SAAoB,GAApBA,SAAoB;KACzB;CAgDJ"}