"""
自动输入工具 - 用于向终端窗口自动发送命令
Auto Input Utils - For automatically sending commands to terminal windows
"""

import time
import pyautogui
import pygetwindow as gw
from typing import List, Optional

class AutoInputManager:
    """自动输入管理器"""
    
    def __init__(self):
        """初始化自动输入管理器"""
        # 设置pyautogui的安全设置
        pyautogui.FAILSAFE = True  # 鼠标移到左上角时停止
        pyautogui.PAUSE = 0.1  # 每个操作间的暂停时间
        
        # 常见终端窗口标题关键词
        self.terminal_keywords = [
            'cmd',
            'powershell',
            'windows terminal',
            'terminal',
            'command prompt',
            '命令提示符',
            'wt',
            'bash',
            'shell'
        ]
    
    def find_terminal_windows(self) -> List[gw.Win32Window]:
        """查找所有可能的终端窗口"""
        terminal_windows = []
        
        try:
            # 获取所有窗口
            all_windows = gw.getAllWindows()
            
            for window in all_windows:
                if window.title and window.visible:
                    title_lower = window.title.lower()
                    # 检查窗口标题是否包含终端关键词
                    for keyword in self.terminal_keywords:
                        if keyword in title_lower:
                            terminal_windows.append(window)
                            break
            
            return terminal_windows
            
        except Exception as e:
            print(f"查找终端窗口时发生错误: {e}")
            return []
    
    def get_most_recent_terminal(self) -> Optional[gw.Win32Window]:
        """获取最近活动的终端窗口"""
        terminal_windows = self.find_terminal_windows()
        
        if not terminal_windows:
            return None
        
        # 返回第一个找到的终端窗口（通常是最近活动的）
        return terminal_windows[0]
    
    def send_command_to_terminal(self, command: str, change_directory: str = None) -> bool:
        """
        向终端发送命令
        
        Args:
            command (str): 要发送的命令
            change_directory (str): 可选的目录切换路径
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 查找终端窗口
            terminal_window = self.get_most_recent_terminal()
            
            if not terminal_window:
                print("未找到终端窗口")
                return False
            
            print(f"找到终端窗口: {terminal_window.title}")
            
            # 激活终端窗口
            try:
                terminal_window.activate()
            except:
                # 如果activate失败，尝试其他方法
                try:
                    terminal_window.minimize()
                    terminal_window.restore()
                except:
                    pass

            time.sleep(0.8)  # 等待窗口激活
            
            # 构建完整命令
            full_command = command
            if change_directory:
                if 'cmd' in terminal_window.title.lower():
                    full_command = f'cd /d "{change_directory}" && {command}'
                else:
                    full_command = f'cd "{change_directory}" && {command}'
            
            # 发送命令
            pyautogui.typewrite(full_command)
            time.sleep(0.2)
            
            # 按回车执行
            pyautogui.press('enter')
            
            print(f"命令已发送到终端: {full_command}")
            return True
            
        except Exception as e:
            print(f"发送命令到终端时发生错误: {e}")
            return False
    
    def list_terminal_windows(self) -> List[str]:
        """列出所有找到的终端窗口标题"""
        terminal_windows = self.find_terminal_windows()
        return [window.title for window in terminal_windows]
    
    def send_text_to_active_window(self, text: str) -> bool:
        """
        向当前活动窗口发送文本（备用方法）
        
        Args:
            text (str): 要发送的文本
            
        Returns:
            bool: 发送是否成功
        """
        try:
            pyautogui.typewrite(text)
            pyautogui.press('enter')
            return True
        except Exception as e:
            print(f"发送文本到活动窗口时发生错误: {e}")
            return False
