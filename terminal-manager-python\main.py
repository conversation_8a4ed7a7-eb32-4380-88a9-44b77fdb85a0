#!/usr/bin/env python3
"""
终端管理工具 - 主程序入口
Terminal Manager - Main Entry Point

功能特性：
- 路径选择（支持拖拽）
- 终端启动
- 命令分类管理
- 主题切换
- 可调整布局
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import TerminalManagerApp

def main():
    """主函数"""
    try:
        # 创建主应用程序
        app = TerminalManagerApp()
        
        # 启动应用程序
        app.run()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
