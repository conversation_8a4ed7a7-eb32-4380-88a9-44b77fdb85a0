import React, { createContext, useContext, useState, ReactNode } from 'react';

interface TerminalState {
  isRunning: boolean;
  currentPath: string;
  output: string;
  isDialogOpen: boolean;
}

interface TerminalContextType {
  terminal: TerminalState;
  setCurrentPath: (path: string) => void;
  startTerminal: () => Promise<void>;
  executeCommand: (command: string) => Promise<void>;
  openDialog: () => void;
  closeDialog: () => void;
  clearOutput: () => void;
}

const TerminalContext = createContext<TerminalContextType | undefined>(undefined);

export const useTerminal = () => {
  const context = useContext(TerminalContext);
  if (context === undefined) {
    throw new Error('useTerminal must be used within a TerminalProvider');
  }
  return context;
};

interface TerminalProviderProps {
  children: ReactNode;
}

export const TerminalProvider: React.FC<TerminalProviderProps> = ({ children }) => {
  const [terminal, setTerminal] = useState<TerminalState>({
    isRunning: false,
    currentPath: '',
    output: '',
    isDialogOpen: false,
  });

  const setCurrentPath = (path: string) => {
    setTerminal(prev => ({ ...prev, currentPath: path }));
  };

  const startTerminal = async () => {
    try {
      setTerminal(prev => ({ ...prev, isRunning: true }));
      
      // 通过IPC与主进程通信启动终端
      const result = await window.electronAPI.startTerminal(terminal.currentPath);
      
      if (result.success) {
        setTerminal(prev => ({ 
          ...prev, 
          output: prev.output + `终端已在路径 ${terminal.currentPath} 启动\n`
        }));
      } else {
        setTerminal(prev => ({ 
          ...prev, 
          output: prev.output + `启动终端失败: ${result.error}\n`,
          isRunning: false
        }));
      }
    } catch (error) {
      console.error('启动终端失败:', error);
      setTerminal(prev => ({ 
        ...prev, 
        output: prev.output + `启动终端失败: ${error}\n`,
        isRunning: false
      }));
    }
  };

  const executeCommand = async (command: string) => {
    try {
      setTerminal(prev => ({ 
        ...prev, 
        output: prev.output + `> ${command}\n`
      }));

      // 异步执行命令
      const result = await window.electronAPI.executeCommand(command);
      
      setTerminal(prev => ({ 
        ...prev, 
        output: prev.output + result.output + '\n'
      }));
    } catch (error) {
      console.error('执行命令失败:', error);
      setTerminal(prev => ({ 
        ...prev, 
        output: prev.output + `命令执行失败: ${error}\n`
      }));
    }
  };

  const openDialog = () => {
    setTerminal(prev => ({ ...prev, isDialogOpen: true }));
  };

  const closeDialog = () => {
    setTerminal(prev => ({ ...prev, isDialogOpen: false }));
  };

  const clearOutput = () => {
    setTerminal(prev => ({ ...prev, output: '' }));
  };

  return (
    <TerminalContext.Provider value={{
      terminal,
      setCurrentPath,
      startTerminal,
      executeCommand,
      openDialog,
      closeDialog,
      clearOutput,
    }}>
      {children}
    </TerminalContext.Provider>
  );
};
