{"name": "@vercel/webpack-asset-relocator-loader", "description": "Asset relocation loader used in ncc for performing Node.js builds while emitting and relocating any asset references.", "version": "1.7.4", "repository": "vercel/webpack-asset-relocator-loader", "license": "MIT", "main": "./dist/index.js", "scripts": {"build": "ncc build src/asset-relocator.js -e resolve", "codecov": "codecov", "test": "npm run build && jest", "test-pnp": "yarn pack -f test/yarn-pnp/assets-plugin-tarball.tgz && cd test/yarn-pnp && echo '' > yarn.lock && YARN_ENABLE_IMMUTABLE_INSTALLS=false yarn install && yarn run build && yarn run test", "test-coverage": "jest --coverage --globals \"{\\\"coverage\\\":true}\" && codecov"}, "files": ["dist/*"], "dependencies": {"resolve": "^1.10.0"}, "devDependencies": {"@vercel/ncc": "^0.38.1", "acorn": "^8.3.0", "acorn-class-fields": "^1.0.0", "acorn-private-class-elements": "^1.0.0", "acorn-static-class-features": "^1.0.0", "bindings": "^1.4.0", "codecov": "^3.8.3", "estree-walker": "^0.6.1", "glob": "^7.1.3", "graceful-fs": "^4.1.15", "jest": "^26.6.3", "loader-utils": "^1.2.3", "magic-string": "^0.25.1", "memory-fs": "^0.4.1", "node-gyp-build": "^4.2.1", "node-pre-gyp": "^0.12.0", "resolve-from": "3.0.0", "rollup-pluginutils": "^2.8.2", "semantic-release": "^17.3.0", "socket.io-client": "^2.2.0", "sourcemap-codec": "^1.4.4", "webpack": "^5", "webpack-cli": "^4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}