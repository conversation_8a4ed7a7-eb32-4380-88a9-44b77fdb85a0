{"name": "node-loader", "version": "2.1.0", "description": "A Node loader module for enhanced-require", "license": "MIT", "repository": "webpack-contrib/node-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/node-loader", "bugs": "https://github.com/webpack-contrib/node-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 10.13.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "pretest:only": "cd test/fixtures/example && npm i", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"webpack": "^5.0.0"}, "dependencies": {"loader-utils": "^2.0.3"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.22.1", "@babel/preset-env": "^7.22.4", "@commitlint/cli": "^12.1.4", "@commitlint/config-conventional": "^12.1.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^27.5.1", "cross-env": "^7.0.3", "cspell": "^6.31.1", "del": "^6.1.1", "del-cli": "^3.0.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "husky": "^6.0.0", "jest": "^27.5.1", "lint-staged": "^10.5.4", "memfs": "^3.5.1", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "standard-version": "^9.5.0", "webpack": "^5.85.0"}, "keywords": ["webpack"]}