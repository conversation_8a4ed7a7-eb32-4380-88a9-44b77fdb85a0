{"version": 3, "sources": ["../../../src/hooks/useDrag/useDragSourceConnector.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { SourceConnector } from '../../internals/index.js'\nimport type {\n\tDragPreviewOptions,\n\tDragSourceOptions,\n} from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDragSourceConnector(\n\tdragSourceOptions: DragSourceOptions | undefined,\n\tdragPreviewOptions: DragPreviewOptions | undefined,\n): SourceConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new SourceConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragSourceOptions = dragSourceOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragSource()\n\t}, [connector, dragSourceOptions])\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dragPreviewOptions = dragPreviewOptions || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDragPreview()\n\t}, [connector, dragPreviewOptions])\n\treturn connector\n}\n"], "names": ["useMemo", "SourceConnector", "useDragDropManager", "useIsomorphicLayoutEffect", "useDragSourceConnector", "dragSourceOptions", "dragPreviewOptions", "manager", "connector", "getBackend", "reconnect", "disconnectDragSource", "disconnectDragPreview"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO,CAAA;AAE/B,SAASC,eAAe,QAAQ,0BAA0B,CAAA;AAK1D,SAASC,kBAAkB,QAAQ,0BAA0B,CAAA;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC,CAAA;AAE3E,OAAO,SAASC,sBAAsB,CACrCC,iBAAgD,EAChDC,kBAAkD,EAChC;IAClB,MAAMC,OAAO,GAAGL,kBAAkB,EAAE;IACpC,MAAMM,SAAS,GAAGR,OAAO,CACxB,IAAM,IAAIC,eAAe,CAACM,OAAO,CAACE,UAAU,EAAE,CAAC;IAAA,EAC/C;QAACF,OAAO;KAAC,CACT;IACDJ,yBAAyB,CAAC,IAAM;QAC/BK,SAAS,CAACH,iBAAiB,GAAGA,iBAAiB,IAAI,IAAI;QACvDG,SAAS,CAACE,SAAS,EAAE;QACrB,OAAO,IAAMF,SAAS,CAACG,oBAAoB,EAAE;QAAA,CAAA;KAC7C,EAAE;QAACH,SAAS;QAAEH,iBAAiB;KAAC,CAAC;IAClCF,yBAAyB,CAAC,IAAM;QAC/BK,SAAS,CAACF,kBAAkB,GAAGA,kBAAkB,IAAI,IAAI;QACzDE,SAAS,CAACE,SAAS,EAAE;QACrB,OAAO,IAAMF,SAAS,CAACI,qBAAqB,EAAE;QAAA,CAAA;KAC9C,EAAE;QAACJ,SAAS;QAAEF,kBAAkB;KAAC,CAAC;IACnC,OAAOE,SAAS,CAAA;CAChB"}