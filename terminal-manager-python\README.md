# 终端管理器 (Terminal Manager)

一个简单易用的终端管理工具，使用Python和tkinter开发。

## 功能特性

- 🗂️ **路径选择**: 支持手动输入、浏览选择和拖拽文件夹
- 🖥️ **终端启动**: 一键在指定路径启动系统终端
- 📋 **命令分类**: 管理和执行常用命令，支持分类组织
- 🎨 **主题切换**: 支持浅色和深色主题
- 📊 **历史记录**: 记录操作历史，便于回顾
- ⚙️ **设置管理**: 可配置的设置和偏好

## 系统要求

- Python 3.6+
- Windows / macOS / Linux

## 安装和运行

1. 克隆或下载项目
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行程序：
   ```bash
   python main.py
   ```

## 支持的终端

### Windows
- Command Prompt (cmd)
- PowerShell
- Windows Terminal (wt)

### macOS
- Terminal
- iTerm2

### Linux
- GNOME Terminal
- XTerm
- Konsole

## 项目结构

```
terminal-manager-python/
├── main.py                 # 主程序入口
├── gui/                    # GUI相关模块
│   ├── __init__.py
│   └── main_window.py      # 主窗口类
├── utils/                  # 工具模块
│   ├── __init__.py
│   ├── terminal_utils.py   # 终端启动工具
│   └── file_utils.py       # 文件管理工具
├── config/                 # 配置模块
│   ├── __init__.py
│   └── settings.py         # 设置管理
├── assets/                 # 资源文件
├── requirements.txt        # 依赖包列表
└── README.md              # 说明文档
```

## 使用说明

1. **选择工作目录**: 在路径输入框中输入路径，或点击"浏览"按钮选择，也可以直接拖拽文件夹到输入框
2. **启动终端**: 点击"启动终端"按钮在选定路径打开系统终端
3. **管理命令**: 在"命令分类"标签页中管理常用命令，支持分类和快速执行
4. **查看历史**: 在"历史记录"标签页中查看操作历史
5. **调整设置**: 在"设置"标签页中配置主题、默认终端等选项

## 配置文件

程序会在以下位置创建配置文件：
- Windows: `%APPDATA%\TerminalManager\`
- macOS/Linux: `~/.terminal-manager/`

配置文件包括：
- `config.json`: 主要设置
- `commands.json`: 命令分类数据
- `history.json`: 历史记录

## 开发

如果你想参与开发或自定义功能：

1. 克隆项目
2. 创建虚拟环境（推荐）
3. 安装开发依赖
4. 修改代码
5. 测试运行

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 更新日志

### v1.0.0
- 初始版本
- 基本的终端启动功能
- 路径选择和拖拽支持
- 命令分类管理
- 主题切换
- 历史记录
