// 简化版本的渲染器，用于测试基础功能
const React = require('react');
const ReactDOM = require('react-dom/client');

// 简单的应用组件
function App() {
  const [currentPath, setCurrentPath] = React.useState('');
  const [terminalOutput, setTerminalOutput] = React.useState('');
  const [isTerminalRunning, setIsTerminalRunning] = React.useState(false);
  const [showDialog, setShowDialog] = React.useState(false);

  // 选择文件夹
  const handleSelectFolder = async () => {
    try {
      const result = await window.electronAPI.selectFolder();
      if (result.success && result.path) {
        setCurrentPath(result.path);
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
    }
  };

  // 启动终端
  const handleStartTerminal = async () => {
    if (!currentPath) {
      alert('请先选择工作目录');
      return;
    }
    
    try {
      const result = await window.electronAPI.startTerminal(currentPath);
      if (result.success) {
        setIsTerminalRunning(true);
        setTerminalOutput(prev => prev + `终端已在路径 ${currentPath} 启动\n`);
      } else {
        alert('启动终端失败: ' + result.error);
      }
    } catch (error) {
      console.error('启动终端失败:', error);
      alert('启动终端失败: ' + error.message);
    }
  };

  // 执行命令
  const handleExecuteCommand = async (command) => {
    if (!isTerminalRunning) {
      alert('请先启动终端');
      return;
    }
    
    try {
      setTerminalOutput(prev => prev + `> ${command}\n`);
      const result = await window.electronAPI.executeCommand(command);
      setTerminalOutput(prev => prev + result.output + '\n');
    } catch (error) {
      console.error('执行命令失败:', error);
      setTerminalOutput(prev => prev + `命令执行失败: ${error.message}\n`);
    }
  };

  return React.createElement('div', { className: 'app' },
    // 标题栏
    React.createElement('header', { className: 'app-header' },
      React.createElement('h1', null, '终端管理器 v1.0'),
      React.createElement('button', { 
        onClick: () => document.documentElement.setAttribute('data-theme', 
          document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark'
        ),
        className: 'theme-toggle'
      }, '🌙/☀️')
    ),
    
    // 控制面板
    React.createElement('div', { className: 'control-panel' },
      React.createElement('div', { className: 'path-selector' },
        React.createElement('label', null, '工作目录:'),
        React.createElement('div', { className: 'path-input-container' },
          React.createElement('input', {
            type: 'text',
            value: currentPath,
            onChange: (e) => setCurrentPath(e.target.value),
            placeholder: '选择工作目录...',
            className: 'path-input'
          }),
          React.createElement('button', {
            onClick: handleSelectFolder,
            className: 'browse-button'
          }, '浏览')
        )
      ),
      
      React.createElement('div', { className: 'terminal-controls' },
        React.createElement('button', {
          onClick: handleStartTerminal,
          disabled: !currentPath || isTerminalRunning,
          className: 'start-button'
        }, isTerminalRunning ? '终端运行中' : '启动终端'),
        
        React.createElement('button', {
          onClick: () => setShowDialog(true),
          disabled: !isTerminalRunning,
          className: 'dialog-button'
        }, '显示对话'),
        
        React.createElement('div', { className: 'status-indicator' },
          React.createElement('span', { 
            className: `status-dot ${isTerminalRunning ? 'running' : 'stopped'}` 
          }),
          React.createElement('span', { className: 'status-text' },
            isTerminalRunning ? '运行中' : '已停止'
          )
        )
      )
    ),
    
    // 命令区域
    React.createElement('div', { className: 'commands-area' },
      React.createElement('h3', null, '常用命令'),
      React.createElement('div', { className: 'command-buttons' },
        React.createElement('button', {
          onClick: () => handleExecuteCommand('dir'),
          disabled: !isTerminalRunning,
          className: 'command-button'
        }, '列出文件 (dir)'),
        
        React.createElement('button', {
          onClick: () => handleExecuteCommand('git status'),
          disabled: !isTerminalRunning,
          className: 'command-button'
        }, 'Git状态'),
        
        React.createElement('button', {
          onClick: () => handleExecuteCommand('npm --version'),
          disabled: !isTerminalRunning,
          className: 'command-button'
        }, 'NPM版本')
      )
    ),
    
    // 终端对话框
    showDialog && React.createElement('div', { className: 'terminal-dialog-overlay' },
      React.createElement('div', { className: 'terminal-dialog' },
        React.createElement('div', { className: 'dialog-header' },
          React.createElement('h3', null, '终端输出'),
          React.createElement('div', { className: 'dialog-controls' },
            React.createElement('button', {
              onClick: () => setTerminalOutput(''),
              className: 'clear-button'
            }, '清空'),
            React.createElement('button', {
              onClick: () => setShowDialog(false),
              className: 'close-button'
            }, '✕')
          )
        ),
        React.createElement('div', { className: 'dialog-content' },
          React.createElement('pre', { className: 'terminal-output' },
            terminalOutput || '暂无输出内容...'
          )
        ),
        React.createElement('div', { className: 'dialog-footer' },
          React.createElement('span', { className: 'output-info' },
            `当前路径: ${currentPath || '未设置'}`
          )
        )
      )
    )
  );
}

// 渲染应用
const container = document.getElementById('root');
const root = ReactDOM.createRoot(container);
root.render(React.createElement(App));
