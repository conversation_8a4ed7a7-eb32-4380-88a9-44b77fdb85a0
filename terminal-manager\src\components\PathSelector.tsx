import React, { useState } from 'react';
import { useDrop } from 'react-dnd';
import { NativeTypes } from 'react-dnd-html5-backend';
import { useTerminal } from '../contexts/TerminalContext';

const PathSelector: React.FC = () => {
  const { terminal, setCurrentPath } = useTerminal();
  const [inputPath, setInputPath] = useState(terminal.currentPath);

  const [{ isOver }, drop] = useDrop(() => ({
    accept: [NativeTypes.FILE],
    drop: (item: any) => {
      const files = item.files;
      if (files && files.length > 0) {
        const file = files[0];
        // 获取文件夹路径
        const path = file.path;
        // 如果是文件，获取其目录
        const folderPath = file.type === '' ? path : path.substring(0, path.lastIndexOf('\\'));
        setInputPath(folderPath);
        setCurrentPath(folderPath);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  const handlePathChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputPath(e.target.value);
  };

  const handlePathSubmit = () => {
    setCurrentPath(inputPath);
  };

  const handleBrowse = async () => {
    try {
      const result = await window.electronAPI.selectFolder();
      if (result.success && result.path) {
        setInputPath(result.path);
        setCurrentPath(result.path);
      }
    } catch (error) {
      console.error('选择文件夹失败:', error);
    }
  };

  return (
    <div className="path-selector">
      <label htmlFor="path-input">工作目录:</label>
      <div 
        ref={drop}
        className={`path-input-container ${isOver ? 'drag-over' : ''}`}
      >
        <input
          id="path-input"
          type="text"
          value={inputPath}
          onChange={handlePathChange}
          placeholder="选择或拖拽文件夹到此处..."
          className="path-input"
        />
        <button onClick={handleBrowse} className="browse-button">
          浏览
        </button>
        <button onClick={handlePathSubmit} className="confirm-button">
          确认
        </button>
      </div>
      {isOver && (
        <div className="drop-indicator">
          释放以选择此文件夹
        </div>
      )}
    </div>
  );
};

export default PathSelector;
