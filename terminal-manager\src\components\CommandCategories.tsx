import React, { useState } from 'react';
import { useCommands } from '../contexts/CommandContext';
import { useTerminal } from '../contexts/TerminalContext';
import CommandButton from './CommandButton';

const CommandCategories: React.FC = () => {
  const { categories } = useCommands();
  const { terminal } = useTerminal();
  const [activeCategory, setActiveCategory] = useState<string>(categories[0]?.id || '');

  const activeCommands = categories.find(cat => cat.id === activeCategory)?.commands || [];

  return (
    <div className="command-categories">
      <div className="category-tabs">
        {categories.map(category => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id)}
            className={`category-tab ${activeCategory === category.id ? 'active' : ''}`}
          >
            {category.name}
          </button>
        ))}
      </div>
      
      <div className="commands-grid">
        {activeCommands.map(command => (
          <CommandButton
            key={command.id}
            command={command}
            disabled={!terminal.isRunning}
          />
        ))}
        
        {activeCommands.length === 0 && (
          <div className="no-commands">
            <p>此分类暂无命令</p>
            <p>请在设置中添加命令</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommandCategories;
