@echo off
REM 终端管理器启动脚本 (Windows)
REM Terminal Manager Startup Script (Windows)

echo 启动终端管理器...
echo Starting Terminal Manager...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    echo Error: Python not found, please install Python 3.6+
    pause
    exit /b 1
)

REM 检查依赖是否安装
python -c "import tkinterdnd2" >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装依赖包...
    echo Installing dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM 启动程序
python main.py

if %errorlevel% neq 0 (
    echo 程序异常退出
    echo Program exited with error
    pause
)
