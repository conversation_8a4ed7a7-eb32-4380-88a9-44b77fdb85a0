{"version": 3, "sources": ["../../src/internals/DropTargetMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>ana<PERSON>,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../types/index.js'\n\nlet isCallingCanDrop = false\n\nexport class DropTargetMonitorImpl implements DropTargetMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate targetId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(targetId: Identifier | null): void {\n\t\tthis.targetId = targetId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.targetId\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic canDrop(): boolean {\n\t\t// Cut out early if the target id has not been set. This should prevent errors\n\t\t// where the user has an older version of dnd-core like in\n\t\t// https://github.com/react-dnd/react-dnd/issues/1310\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingCanDrop,\n\t\t\t'You may not call monitor.canDrop() inside your canDrop() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrop = true\n\t\t\treturn this.internalMonitor.canDropOnTarget(this.targetId)\n\t\t} finally {\n\t\t\tisCallingCanDrop = false\n\t\t}\n\t}\n\n\tpublic isOver(options?: { shallow?: boolean }): boolean {\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\treturn this.internalMonitor.isOverTarget(this.targetId, options)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n"], "names": ["invariant", "isCallingCanDrop", "DropTargetMonitorImpl", "receiveHandlerId", "targetId", "getHandlerId", "subscribeToStateChange", "listener", "options", "internalMonitor", "canDrop", "canDropOnTarget", "isOver", "isOverTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "manager", "getMonitor"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAYhD,IAAIC,gBAAgB,GAAG,KAAK;AAE5B,OAAO,MAAMC,qBAAqB;IAQjC,AAAOC,gBAAgB,CAACC,QAA2B,EAAQ;QAC1D,IAAI,CAACA,QAAQ,GAAGA,QAAQ;KACxB;IAED,AAAOC,YAAY,GAAsB;QACxC,OAAO,IAAI,CAACD,QAAQ,CAAA;KACpB;IAED,AAAOE,sBAAsB,CAC5BC,QAAkB,EAClBC,OAAuC,EACzB;QACd,OAAO,IAAI,CAACC,eAAe,CAACH,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC,CAAA;KACrE;IAED,AAAOE,OAAO,GAAY;QACzB,8EAA8E;QAC9E,0DAA0D;QAC1D,qDAAqD;QACrD,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;YACnB,OAAO,KAAK,CAAA;SACZ;QACDJ,SAAS,CACR,CAACC,gBAAgB,EACjB,2EAA2E,GAC1E,8EAA8E,CAC/E;QAED,IAAI;YACHA,gBAAgB,GAAG,IAAI;YACvB,OAAO,IAAI,CAACQ,eAAe,CAACE,eAAe,CAAC,IAAI,CAACP,QAAQ,CAAC,CAAA;SAC1D,QAAS;YACTH,gBAAgB,GAAG,KAAK;SACxB;KACD;IAED,AAAOW,MAAM,CAACJ,OAA+B,EAAW;QACvD,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;YACnB,OAAO,KAAK,CAAA;SACZ;QACD,OAAO,IAAI,CAACK,eAAe,CAACI,YAAY,CAAC,IAAI,CAACT,QAAQ,EAAEI,OAAO,CAAC,CAAA;KAChE;IAED,AAAOM,WAAW,GAAsB;QACvC,OAAO,IAAI,CAACL,eAAe,CAACK,WAAW,EAAE,CAAA;KACzC;IAED,AAAOC,OAAO,GAAQ;QACrB,OAAO,IAAI,CAACN,eAAe,CAACM,OAAO,EAAE,CAAA;KACrC;IAED,AAAOC,aAAa,GAAQ;QAC3B,OAAO,IAAI,CAACP,eAAe,CAACO,aAAa,EAAE,CAAA;KAC3C;IAED,AAAOC,OAAO,GAAY;QACzB,OAAO,IAAI,CAACR,eAAe,CAACQ,OAAO,EAAE,CAAA;KACrC;IAED,AAAOC,sBAAsB,GAAmB;QAC/C,OAAO,IAAI,CAACT,eAAe,CAACS,sBAAsB,EAAE,CAAA;KACpD;IAED,AAAOC,4BAA4B,GAAmB;QACrD,OAAO,IAAI,CAACV,eAAe,CAACU,4BAA4B,EAAE,CAAA;KAC1D;IAED,AAAOC,qBAAqB,GAAmB;QAC9C,OAAO,IAAI,CAACX,eAAe,CAACW,qBAAqB,EAAE,CAAA;KACnD;IAED,AAAOC,eAAe,GAAmB;QACxC,OAAO,IAAI,CAACZ,eAAe,CAACY,eAAe,EAAE,CAAA;KAC7C;IAED,AAAOC,8BAA8B,GAAmB;QACvD,OAAO,IAAI,CAACb,eAAe,CAACa,8BAA8B,EAAE,CAAA;KAC5D;IAjFD,YAAmBC,OAAwB,CAAE;QAF7C,KAAQnB,QAAQ,GAAsB,IAAI,AAhB3C,CAgB2C;QAGzC,IAAI,CAACK,eAAe,GAAGc,OAAO,CAACC,UAAU,EAAE;KAC3C;CAgFD"}