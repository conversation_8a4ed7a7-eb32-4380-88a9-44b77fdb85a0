import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface Command {
  id: string;
  name: string;
  command: string;
  categoryId: string;
}

export interface Category {
  id: string;
  name: string;
  commands: Command[];
}

interface CommandContextType {
  categories: Category[];
  addCategory: (name: string) => void;
  removeCategory: (id: string) => void;
  updateCategory: (id: string, name: string) => void;
  addCommand: (categoryId: string, name: string, command: string) => void;
  removeCommand: (categoryId: string, commandId: string) => void;
  updateCommand: (categoryId: string, commandId: string, name: string, command: string) => void;
}

const CommandContext = createContext<CommandContextType | undefined>(undefined);

export const useCommands = () => {
  const context = useContext(CommandContext);
  if (context === undefined) {
    throw new Error('useCommands must be used within a CommandProvider');
  }
  return context;
};

interface CommandProviderProps {
  children: ReactNode;
}

export const CommandProvider: React.FC<CommandProviderProps> = ({ children }) => {
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    // 从本地存储加载命令配置
    const savedCategories = localStorage.getItem('terminal-manager-categories');
    if (savedCategories) {
      setCategories(JSON.parse(savedCategories));
    } else {
      // 初始化默认分类
      const defaultCategories: Category[] = [
        {
          id: '1',
          name: 'Git命令',
          commands: [
            { id: '1', name: 'Git状态', command: 'git status', categoryId: '1' },
            { id: '2', name: 'Git提交', command: 'git add . && git commit -m "update"', categoryId: '1' },
          ]
        },
        {
          id: '2',
          name: '系统命令',
          commands: [
            { id: '3', name: '列出文件', command: 'ls -la', categoryId: '2' },
            { id: '4', name: '当前目录', command: 'pwd', categoryId: '2' },
          ]
        }
      ];
      setCategories(defaultCategories);
    }
  }, []);

  useEffect(() => {
    // 保存到本地存储
    localStorage.setItem('terminal-manager-categories', JSON.stringify(categories));
  }, [categories]);

  const addCategory = (name: string) => {
    const newCategory: Category = {
      id: Date.now().toString(),
      name,
      commands: []
    };
    setCategories(prev => [...prev, newCategory]);
  };

  const removeCategory = (id: string) => {
    setCategories(prev => prev.filter(cat => cat.id !== id));
  };

  const updateCategory = (id: string, name: string) => {
    setCategories(prev => prev.map(cat => 
      cat.id === id ? { ...cat, name } : cat
    ));
  };

  const addCommand = (categoryId: string, name: string, command: string) => {
    const newCommand: Command = {
      id: Date.now().toString(),
      name,
      command,
      categoryId
    };
    
    setCategories(prev => prev.map(cat => 
      cat.id === categoryId 
        ? { ...cat, commands: [...cat.commands, newCommand] }
        : cat
    ));
  };

  const removeCommand = (categoryId: string, commandId: string) => {
    setCategories(prev => prev.map(cat => 
      cat.id === categoryId 
        ? { ...cat, commands: cat.commands.filter(cmd => cmd.id !== commandId) }
        : cat
    ));
  };

  const updateCommand = (categoryId: string, commandId: string, name: string, command: string) => {
    setCategories(prev => prev.map(cat => 
      cat.id === categoryId 
        ? { 
            ...cat, 
            commands: cat.commands.map(cmd => 
              cmd.id === commandId ? { ...cmd, name, command } : cmd
            )
          }
        : cat
    ));
  };

  return (
    <CommandContext.Provider value={{
      categories,
      addCategory,
      removeCategory,
      updateCategory,
      addCommand,
      removeCommand,
      updateCommand,
    }}>
      {children}
    </CommandContext.Provider>
  );
};
