"""
主窗口类 - 终端管理工具的主界面
Main Window Class - Main interface for Terminal Manager
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import subprocess
import platform
import json
from tkinterdnd2 import DND_FILES, TkinterDnD

# 添加父目录到路径以便导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.terminal_utils import TerminalLauncher
from utils.file_utils import FileManager
from config.settings import SettingsManager

class TerminalManagerApp:
    """终端管理工具主应用程序类"""
    
    def __init__(self):
        """初始化应用程序"""
        # 创建主窗口
        self.root = TkinterDnD.Tk()
        self.root.title("终端管理器 v1.0")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
        
        # 初始化组件
        self.settings_manager = SettingsManager()
        self.terminal_launcher = TerminalLauncher()
        self.file_manager = FileManager()
        
        # 应用程序状态
        self.current_path = tk.StringVar(value=os.getcwd())
        self.theme_mode = tk.StringVar(value=self.settings_manager.get_theme())
        
        # 命令分类数据
        self.command_categories = self.settings_manager.get_command_categories()
        
        # 创建界面
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()
        self.apply_theme()
        
        # 加载保存的设置
        self.load_settings()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 标题栏
        self.create_header()
        
        # 控制面板
        self.create_control_panel()
        
        # 主内容区域
        self.create_main_content()
        
        # 状态栏
        self.create_status_bar()
    
    def create_header(self):
        """创建标题栏"""
        self.header_frame = ttk.Frame(self.main_frame)
        
        # 标题
        title_label = ttk.Label(
            self.header_frame, 
            text="终端管理器", 
            font=("Microsoft YaHei", 16, "bold")
        )
        title_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # 主题切换按钮
        self.theme_button = ttk.Button(
            self.header_frame,
            text="🌙",
            width=3,
            command=self.toggle_theme
        )
        self.theme_button.pack(side=tk.RIGHT, padx=10, pady=5)
        
        # 设置按钮
        settings_button = ttk.Button(
            self.header_frame,
            text="⚙️",
            width=3,
            command=self.open_settings
        )
        settings_button.pack(side=tk.RIGHT, padx=5, pady=5)
    
    def create_control_panel(self):
        """创建控制面板"""
        self.control_frame = ttk.LabelFrame(self.main_frame, text="控制面板", padding=10)
        
        # 路径选择区域
        path_frame = ttk.Frame(self.control_frame)
        
        ttk.Label(path_frame, text="工作目录:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.path_entry = ttk.Entry(
            path_frame, 
            textvariable=self.current_path,
            width=50
        )
        self.path_entry.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        
        # 浏览按钮
        browse_button = ttk.Button(
            path_frame,
            text="浏览",
            command=self.browse_folder
        )
        browse_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        path_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 终端控制区域
        terminal_frame = ttk.Frame(self.control_frame)
        
        # 启动终端按钮
        self.start_terminal_button = ttk.Button(
            terminal_frame,
            text="🖥️ 启动终端",
            command=self.start_terminal,
            style="Accent.TButton"
        )
        self.start_terminal_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态指示器
        self.status_label = ttk.Label(
            terminal_frame,
            text="● 就绪",
            foreground="green"
        )
        self.status_label.pack(side=tk.LEFT)
        
        terminal_frame.pack(fill=tk.X)
        
        # 设置拖拽支持
        self.setup_drag_drop()
    
    def create_main_content(self):
        """创建主内容区域"""
        # 创建Notebook用于标签页
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 命令分类标签页
        self.create_commands_tab()
        
        # 历史记录标签页
        self.create_history_tab()
        
        # 设置标签页
        self.create_settings_tab()
    
    def create_commands_tab(self):
        """创建命令分类标签页"""
        commands_frame = ttk.Frame(self.notebook)
        self.notebook.add(commands_frame, text="命令分类")
        
        # 左侧分类列表
        left_frame = ttk.Frame(commands_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        
        ttk.Label(left_frame, text="分类", font=("Microsoft YaHei", 10, "bold")).pack(pady=(0, 5))
        
        # 分类列表框
        self.category_listbox = tk.Listbox(left_frame, width=20, height=15)
        self.category_listbox.pack(fill=tk.BOTH, expand=True)
        self.category_listbox.bind('<<ListboxSelect>>', self.on_category_select)
        
        # 分类管理按钮
        category_buttons_frame = ttk.Frame(left_frame)
        category_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(category_buttons_frame, text="添加", command=self.add_category).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(category_buttons_frame, text="删除", command=self.delete_category).pack(side=tk.LEFT, padx=2)
        ttk.Button(category_buttons_frame, text="编辑", command=self.edit_category).pack(side=tk.LEFT, padx=(2, 0))
        
        # 右侧命令列表
        right_frame = ttk.Frame(commands_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        ttk.Label(right_frame, text="命令", font=("Microsoft YaHei", 10, "bold")).pack(pady=(0, 5))
        
        # 命令列表框架
        commands_list_frame = ttk.Frame(right_frame)
        commands_list_frame.pack(fill=tk.BOTH, expand=True)
        
        # 这里将用来显示当前分类的命令按钮
        self.commands_display_frame = ttk.Frame(commands_list_frame)
        self.commands_display_frame.pack(fill=tk.BOTH, expand=True)
        
        # 命令管理按钮
        command_buttons_frame = ttk.Frame(right_frame)
        command_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(command_buttons_frame, text="添加命令", command=self.add_command).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(command_buttons_frame, text="编辑命令", command=self.edit_command).pack(side=tk.LEFT, padx=2)
        ttk.Button(command_buttons_frame, text="删除命令", command=self.delete_command).pack(side=tk.LEFT, padx=(2, 0))
        
        # 加载分类数据
        self.refresh_categories()
    
    def create_history_tab(self):
        """创建历史记录标签页"""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="历史记录")
        
        # 历史记录列表
        self.history_text = tk.Text(history_frame, wrap=tk.WORD, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=scrollbar.set)
        
        self.history_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 加载历史记录
        self.load_history()
    
    def create_settings_tab(self):
        """创建设置标签页"""
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="设置")
        
        # 主题设置
        theme_frame = ttk.LabelFrame(settings_frame, text="主题设置", padding=10)
        theme_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Radiobutton(
            theme_frame, 
            text="浅色主题", 
            variable=self.theme_mode, 
            value="light",
            command=self.apply_theme
        ).pack(anchor=tk.W)
        
        ttk.Radiobutton(
            theme_frame, 
            text="深色主题", 
            variable=self.theme_mode, 
            value="dark",
            command=self.apply_theme
        ).pack(anchor=tk.W)
        
        # 终端设置
        terminal_frame = ttk.LabelFrame(settings_frame, text="终端设置", padding=10)
        terminal_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(terminal_frame, text="默认终端:").pack(anchor=tk.W)
        self.terminal_var = tk.StringVar(value=self.settings_manager.get_default_terminal())
        
        if platform.system() == "Windows":
            terminals = ["cmd", "powershell", "wt"]
        elif platform.system() == "Darwin":
            terminals = ["Terminal", "iTerm"]
        else:
            terminals = ["gnome-terminal", "xterm", "konsole"]
        
        for terminal in terminals:
            ttk.Radiobutton(
                terminal_frame,
                text=terminal,
                variable=self.terminal_var,
                value=terminal
            ).pack(anchor=tk.W)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.main_frame)
        
        self.status_text = ttk.Label(
            self.status_frame,
            text="就绪",
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status_text.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2, pady=2)
        
        # 当前路径显示
        self.path_status = ttk.Label(
            self.status_frame,
            text=f"路径: {self.current_path.get()}",
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.path_status.pack(side=tk.RIGHT, padx=2, pady=2)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.header_frame.pack(fill=tk.X, pady=(0, 5))
        self.control_frame.pack(fill=tk.X, pady=(0, 5))
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.status_frame.pack(fill=tk.X)
    
    def setup_bindings(self):
        """设置事件绑定"""
        # 路径变化时更新状态栏
        self.current_path.trace('w', self.on_path_change)
        
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_drag_drop(self):
        """设置拖拽支持"""
        # 为路径输入框设置拖拽支持
        self.path_entry.drop_target_register(DND_FILES)
        self.path_entry.dnd_bind('<<Drop>>', self.on_drop)
    
    def on_drop(self, event):
        """处理拖拽事件"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if os.path.isdir(file_path):
                self.current_path.set(file_path)
            else:
                # 如果是文件，使用文件所在目录
                self.current_path.set(os.path.dirname(file_path))
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(
            title="选择工作目录",
            initialdir=self.current_path.get()
        )
        if folder:
            self.current_path.set(folder)
    
    def start_terminal(self):
        """启动终端"""
        path = self.current_path.get()
        if not os.path.exists(path):
            messagebox.showerror("错误", f"路径不存在: {path}")
            return
        
        try:
            self.status_label.config(text="● 启动中...", foreground="orange")
            self.root.update()
            
            # 使用终端启动器启动终端
            success = self.terminal_launcher.launch_terminal(path, self.terminal_var.get())
            
            if success:
                self.status_label.config(text="● 已启动", foreground="green")
                self.add_to_history(f"启动终端: {path}")
                self.update_status(f"终端已在 {path} 启动")
            else:
                self.status_label.config(text="● 启动失败", foreground="red")
                messagebox.showerror("错误", "启动终端失败")
                
        except Exception as e:
            self.status_label.config(text="● 错误", foreground="red")
            messagebox.showerror("错误", f"启动终端时发生错误: {str(e)}")
    
    def toggle_theme(self):
        """切换主题"""
        current = self.theme_mode.get()
        new_theme = "dark" if current == "light" else "light"
        self.theme_mode.set(new_theme)
        self.apply_theme()
    
    def apply_theme(self):
        """应用主题"""
        theme = self.theme_mode.get()
        
        if theme == "dark":
            self.theme_button.config(text="☀️")
            # 这里可以设置深色主题的颜色
        else:
            self.theme_button.config(text="🌙")
            # 这里可以设置浅色主题的颜色
        
        # 保存主题设置
        self.settings_manager.set_theme(theme)
    
    def on_path_change(self, *args):
        """路径变化时的回调"""
        path = self.current_path.get()
        self.path_status.config(text=f"路径: {path}")
    
    def on_category_select(self, event):
        """分类选择事件"""
        selection = self.category_listbox.curselection()
        if selection:
            category_name = self.category_listbox.get(selection[0])
            self.display_commands(category_name)
    
    def refresh_categories(self):
        """刷新分类列表"""
        self.category_listbox.delete(0, tk.END)
        for category in self.command_categories:
            self.category_listbox.insert(tk.END, category['name'])
    
    def display_commands(self, category_name):
        """显示指定分类的命令"""
        # 清空当前显示
        for widget in self.commands_display_frame.winfo_children():
            widget.destroy()
        
        # 找到对应分类
        category = next((cat for cat in self.command_categories if cat['name'] == category_name), None)
        if not category:
            return
        
        # 创建命令按钮
        row = 0
        col = 0
        for command in category.get('commands', []):
            btn = ttk.Button(
                self.commands_display_frame,
                text=command['name'],
                command=lambda cmd=command['command']: self.execute_command(cmd)
            )
            btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
            
            col += 1
            if col >= 3:  # 每行3个按钮
                col = 0
                row += 1
        
        # 配置列权重
        for i in range(3):
            self.commands_display_frame.columnconfigure(i, weight=1)
    
    def execute_command(self, command):
        """执行命令"""
        path = self.current_path.get()
        try:
            # 在指定路径下执行命令
            result = subprocess.run(
                command,
                shell=True,
                cwd=path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            self.add_to_history(f"执行命令: {command} (在 {path})")
            if result.stdout:
                self.add_to_history(f"输出: {result.stdout}")
            if result.stderr:
                self.add_to_history(f"错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            self.add_to_history(f"命令超时: {command}")
        except Exception as e:
            self.add_to_history(f"执行失败: {command} - {str(e)}")
    
    def add_category(self):
        """添加分类"""
        # 这里可以打开一个对话框来添加分类
        pass
    
    def delete_category(self):
        """删除分类"""
        # 这里可以删除选中的分类
        pass
    
    def edit_category(self):
        """编辑分类"""
        # 这里可以编辑选中的分类
        pass
    
    def add_command(self):
        """添加命令"""
        # 这里可以打开一个对话框来添加命令
        pass
    
    def edit_command(self):
        """编辑命令"""
        # 这里可以编辑选中的命令
        pass
    
    def delete_command(self):
        """删除命令"""
        # 这里可以删除选中的命令
        pass
    
    def open_settings(self):
        """打开设置"""
        # 切换到设置标签页
        self.notebook.select(2)
    
    def add_to_history(self, message):
        """添加到历史记录"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.history_text.config(state=tk.NORMAL)
        self.history_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.history_text.config(state=tk.DISABLED)
        self.history_text.see(tk.END)
        
        # 保存到文件
        self.settings_manager.add_history_entry(message)
    
    def load_history(self):
        """加载历史记录"""
        history = self.settings_manager.get_history()
        self.history_text.config(state=tk.NORMAL)
        for entry in history:
            self.history_text.insert(tk.END, f"{entry}\n")
        self.history_text.config(state=tk.DISABLED)
    
    def update_status(self, message):
        """更新状态栏"""
        self.status_text.config(text=message)
    
    def load_settings(self):
        """加载设置"""
        # 加载保存的路径
        saved_path = self.settings_manager.get_last_path()
        if saved_path and os.path.exists(saved_path):
            self.current_path.set(saved_path)
    
    def save_settings(self):
        """保存设置"""
        self.settings_manager.set_last_path(self.current_path.get())
        self.settings_manager.set_default_terminal(self.terminal_var.get())
        self.settings_manager.save()
    
    def on_closing(self):
        """窗口关闭时的处理"""
        self.save_settings()
        self.root.destroy()
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()
