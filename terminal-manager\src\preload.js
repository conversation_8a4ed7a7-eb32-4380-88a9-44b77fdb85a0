const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  selectFolder: () => ipcRenderer.invoke('select-folder'),
  startTerminal: (path) => ipcRenderer.invoke('start-terminal', path),
  executeCommand: (command) => ipcRenderer.invoke('execute-command', command),
});
