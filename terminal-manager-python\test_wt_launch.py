#!/usr/bin/env python3
"""
Windows Terminal启动测试脚本
Test script for Windows Terminal launch
"""

import os
from utils.terminal_utils import TerminalLauncher

def test_wt_launch():
    """测试Windows Terminal启动"""
    print("=== Windows Terminal启动测试 ===")
    
    launcher = TerminalLauncher()
    current_path = os.getcwd()
    
    print(f"当前路径: {current_path}")
    print("正在启动Windows Terminal...")
    
    # 测试启动Windows Terminal
    success = launcher.launch_terminal(current_path, "wt")
    
    if success:
        print("✅ Windows Terminal启动成功！")
        print("请检查是否显示了帮助界面。")
    else:
        print("❌ Windows Terminal启动失败")

if __name__ == "__main__":
    test_wt_launch()
