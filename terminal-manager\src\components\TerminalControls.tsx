import React from 'react';
import { useTerminal } from '../contexts/TerminalContext';

const TerminalControls: React.FC = () => {
  const { terminal, startTerminal, openDialog } = useTerminal();

  const handleStartTerminal = async () => {
    if (!terminal.currentPath) {
      alert('请先选择工作目录');
      return;
    }
    await startTerminal();
  };

  return (
    <div className="terminal-controls">
      <button 
        onClick={handleStartTerminal}
        disabled={!terminal.currentPath || terminal.isRunning}
        className="start-button"
      >
        {terminal.isRunning ? '终端运行中' : '启动终端'}
      </button>
      
      <button 
        onClick={openDialog}
        disabled={!terminal.isRunning}
        className="dialog-button"
      >
        显示对话
      </button>
      
      <div className="status-indicator">
        <span className={`status-dot ${terminal.isRunning ? 'running' : 'stopped'}`}></span>
        <span className="status-text">
          {terminal.isRunning ? '运行中' : '已停止'}
        </span>
      </div>
    </div>
  );
};

export default TerminalControls;
