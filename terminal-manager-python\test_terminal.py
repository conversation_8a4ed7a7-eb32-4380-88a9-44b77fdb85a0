#!/usr/bin/env python3
"""
终端启动测试脚本
Test script for terminal launching functionality
"""

import os
import sys
from utils.terminal_utils import TerminalLauncher

def test_terminal_launcher():
    """测试终端启动器"""
    print("=== 终端启动器测试 ===")
    
    launcher = TerminalLauncher()
    
    # 获取当前目录
    current_dir = os.getcwd()
    print(f"当前目录: {current_dir}")
    
    # 获取可用终端列表
    available_terminals = launcher.get_available_terminals()
    print(f"可用终端: {available_terminals}")
    
    if available_terminals:
        # 测试启动第一个可用终端
        terminal_type = available_terminals[0]
        print(f"尝试启动终端: {terminal_type}")
        
        try:
            success = launcher.launch_terminal(current_dir, terminal_type)
            if success:
                print("✅ 终端启动成功!")
            else:
                print("❌ 终端启动失败!")
        except Exception as e:
            print(f"❌ 启动终端时发生错误: {e}")
    else:
        print("❌ 没有找到可用的终端")

if __name__ == "__main__":
    test_terminal_launcher()
