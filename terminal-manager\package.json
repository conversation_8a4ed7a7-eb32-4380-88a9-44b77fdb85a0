{"name": "terminal-manager", "productName": "terminal-manager", "version": "1.0.0", "description": "My Electron application description", "main": "src/index.js", "scripts": {"start": "electron-forge start", "package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "echo \"No linting configured\""}, "keywords": [], "author": "a3960", "license": "MIT", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "electron-squirrel-startup": "^1.0.1", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-resizable-panels": "^3.0.3"}, "devDependencies": {"@electron-forge/cli": "^7.8.1", "@electron-forge/maker-deb": "^7.8.1", "@electron-forge/maker-rpm": "^7.8.1", "@electron-forge/maker-squirrel": "^7.8.1", "@electron-forge/maker-zip": "^7.8.1", "@electron-forge/plugin-auto-unpack-natives": "^7.8.1", "@electron-forge/plugin-fuses": "^7.8.1", "@electron-forge/plugin-webpack": "^7.8.1", "@electron/fuses": "^1.8.0", "@vercel/webpack-asset-relocator-loader": "^1.7.4", "css-loader": "^7.1.2", "electron": "37.1.0", "fork-ts-checker-webpack-plugin": "^9.1.0", "node-loader": "^2.1.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}