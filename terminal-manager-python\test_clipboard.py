#!/usr/bin/env python3
"""
剪贴板功能测试脚本
Test script for clipboard functionality
"""

import tkinter as tk
import os

def test_clipboard():
    """测试剪贴板功能"""
    print("=== 剪贴板功能测试 ===")
    
    # 创建一个简单的tkinter窗口来测试剪贴板
    root = tk.Tk()
    root.withdraw()  # 隐藏窗口
    
    # 测试命令
    test_command = "dir" if os.name == 'nt' else "ls -la"
    test_path = os.getcwd()
    
    # 构建完整命令
    if os.name == 'nt':  # Windows
        full_command = f'cd /d "{test_path}" && {test_command}'
    else:  # Unix-like systems
        full_command = f'cd "{test_path}" && {test_command}'
    
    print(f"测试命令: {test_command}")
    print(f"测试路径: {test_path}")
    print(f"完整命令: {full_command}")
    
    try:
        # 复制到剪贴板
        root.clipboard_clear()
        root.clipboard_append(full_command)
        root.update()
        
        print("✅ 命令已复制到剪贴板")
        print("请在终端中按 Ctrl+V (或 Cmd+V) 粘贴测试")
        
        # 验证剪贴板内容
        clipboard_content = root.clipboard_get()
        print(f"剪贴板内容: {clipboard_content}")
        
        if clipboard_content == full_command:
            print("✅ 剪贴板内容验证成功")
        else:
            print("❌ 剪贴板内容验证失败")
            
    except Exception as e:
        print(f"❌ 剪贴板操作失败: {e}")
    
    finally:
        root.destroy()

if __name__ == "__main__":
    test_clipboard()
