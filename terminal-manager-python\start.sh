#!/bin/bash
# 终端管理器启动脚本 (Linux/macOS)
# Terminal Manager Startup Script (Linux/macOS)

echo "启动终端管理器..."
echo "Starting Terminal Manager..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.6+"
        echo "Error: Python not found, please install Python 3.6+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python: $PYTHON_CMD"
echo "Using Python: $PYTHON_CMD"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python版本: $PYTHON_VERSION"

# 检查依赖是否安装
$PYTHON_CMD -c "import tkinterdnd2" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装依赖包..."
    echo "Installing dependencies..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

# 启动程序
$PYTHON_CMD main.py

if [ $? -ne 0 ]; then
    echo "程序异常退出"
    echo "Program exited with error"
    read -p "按任意键继续... Press any key to continue..."
fi
