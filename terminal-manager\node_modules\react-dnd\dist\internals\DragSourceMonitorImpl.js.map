{"version": 3, "sources": ["../../src/internals/DragSourceMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON>agDropManager,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DragSourceMonitor } from '../types/index.js'\n\nlet isCallingCanDrag = false\nlet isCallingIsDragging = false\n\nexport class DragSourceMonitorImpl implements DragSourceMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate sourceId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(sourceId: Identifier | null): void {\n\t\tthis.sourceId = sourceId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.sourceId\n\t}\n\n\tpublic canDrag(): boolean {\n\t\tinvariant(\n\t\t\t!isCallingCanDrag,\n\t\t\t'You may not call monitor.canDrag() inside your canDrag() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrag = true\n\t\t\treturn this.internalMonitor.canDragSource(this.sourceId as Identifier)\n\t\t} finally {\n\t\t\tisCallingCanDrag = false\n\t\t}\n\t}\n\n\tpublic isDragging(): boolean {\n\t\tif (!this.sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingIsDragging,\n\t\t\t'You may not call monitor.isDragging() inside your isDragging() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingIsDragging = true\n\t\t\treturn this.internalMonitor.isDraggingSource(this.sourceId)\n\t\t} finally {\n\t\t\tisCallingIsDragging = false\n\t\t}\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic isDraggingSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.isDraggingSource(sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: Identifier,\n\t\toptions?: { shallow: boolean },\n\t): boolean {\n\t\treturn this.internalMonitor.isOverTarget(targetId, options)\n\t}\n\n\tpublic getTargetIds(): Identifier[] {\n\t\treturn this.internalMonitor.getTargetIds()\n\t}\n\n\tpublic isSourcePublic(): boolean | null {\n\t\treturn this.internalMonitor.isSourcePublic()\n\t}\n\n\tpublic getSourceId(): Identifier | null {\n\t\treturn this.internalMonitor.getSourceId()\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToOffsetChange(listener)\n\t}\n\n\tpublic canDragSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDragSource(sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDropOnTarget(targetId)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n"], "names": ["invariant", "isCallingCanDrag", "isCallingIsDragging", "DragSourceMonitorImpl", "receiveHandlerId", "sourceId", "getHandlerId", "canDrag", "internalMonitor", "canDragSource", "isDragging", "isDraggingSource", "subscribeToStateChange", "listener", "options", "isOverTarget", "targetId", "getTargetIds", "isSourcePublic", "getSourceId", "subscribeToOffsetChange", "canDropOnTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "manager", "getMonitor"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB,CAAA;AAYhD,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,mBAAmB,GAAG,KAAK;AAE/B,OAAO,MAAMC,qBAAqB;IAQjC,AAAOC,gBAAgB,CAACC,QAA2B,EAAQ;QAC1D,IAAI,CAACA,QAAQ,GAAGA,QAAQ;KACxB;IAED,AAAOC,YAAY,GAAsB;QACxC,OAAO,IAAI,CAACD,QAAQ,CAAA;KACpB;IAED,AAAOE,OAAO,GAAY;QACzBP,SAAS,CACR,CAACC,gBAAgB,EACjB,2EAA2E,GAC1E,8EAA8E,CAC/E;QAED,IAAI;YACHA,gBAAgB,GAAG,IAAI;YACvB,OAAO,IAAI,CAACO,eAAe,CAACC,aAAa,CAAC,IAAI,CAACJ,QAAQ,CAAe,CAAA;SACtE,QAAS;YACTJ,gBAAgB,GAAG,KAAK;SACxB;KACD;IAED,AAAOS,UAAU,GAAY;QAC5B,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE;YACnB,OAAO,KAAK,CAAA;SACZ;QACDL,SAAS,CACR,CAACE,mBAAmB,EACpB,iFAAiF,GAChF,8EAA8E,CAC/E;QAED,IAAI;YACHA,mBAAmB,GAAG,IAAI;YAC1B,OAAO,IAAI,CAACM,eAAe,CAACG,gBAAgB,CAAC,IAAI,CAACN,QAAQ,CAAC,CAAA;SAC3D,QAAS;YACTH,mBAAmB,GAAG,KAAK;SAC3B;KACD;IAED,AAAOU,sBAAsB,CAC5BC,QAAkB,EAClBC,OAAuC,EACzB;QACd,OAAO,IAAI,CAACN,eAAe,CAACI,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC,CAAA;KACrE;IAED,AAAOH,gBAAgB,CAACN,QAAoB,EAAW;QACtD,OAAO,IAAI,CAACG,eAAe,CAACG,gBAAgB,CAACN,QAAQ,CAAC,CAAA;KACtD;IAED,AAAOU,YAAY,CAClBC,QAAoB,EACpBF,OAA8B,EACpB;QACV,OAAO,IAAI,CAACN,eAAe,CAACO,YAAY,CAACC,QAAQ,EAAEF,OAAO,CAAC,CAAA;KAC3D;IAED,AAAOG,YAAY,GAAiB;QACnC,OAAO,IAAI,CAACT,eAAe,CAACS,YAAY,EAAE,CAAA;KAC1C;IAED,AAAOC,cAAc,GAAmB;QACvC,OAAO,IAAI,CAACV,eAAe,CAACU,cAAc,EAAE,CAAA;KAC5C;IAED,AAAOC,WAAW,GAAsB;QACvC,OAAO,IAAI,CAACX,eAAe,CAACW,WAAW,EAAE,CAAA;KACzC;IAED,AAAOC,uBAAuB,CAACP,QAAkB,EAAe;QAC/D,OAAO,IAAI,CAACL,eAAe,CAACY,uBAAuB,CAACP,QAAQ,CAAC,CAAA;KAC7D;IAED,AAAOJ,aAAa,CAACJ,QAAoB,EAAW;QACnD,OAAO,IAAI,CAACG,eAAe,CAACC,aAAa,CAACJ,QAAQ,CAAC,CAAA;KACnD;IAED,AAAOgB,eAAe,CAACL,QAAoB,EAAW;QACrD,OAAO,IAAI,CAACR,eAAe,CAACa,eAAe,CAACL,QAAQ,CAAC,CAAA;KACrD;IAED,AAAOM,WAAW,GAAsB;QACvC,OAAO,IAAI,CAACd,eAAe,CAACc,WAAW,EAAE,CAAA;KACzC;IAED,AAAOC,OAAO,GAAQ;QACrB,OAAO,IAAI,CAACf,eAAe,CAACe,OAAO,EAAE,CAAA;KACrC;IAED,AAAOC,aAAa,GAAQ;QAC3B,OAAO,IAAI,CAAChB,eAAe,CAACgB,aAAa,EAAE,CAAA;KAC3C;IAED,AAAOC,OAAO,GAAY;QACzB,OAAO,IAAI,CAACjB,eAAe,CAACiB,OAAO,EAAE,CAAA;KACrC;IAED,AAAOC,sBAAsB,GAAmB;QAC/C,OAAO,IAAI,CAAClB,eAAe,CAACkB,sBAAsB,EAAE,CAAA;KACpD;IAED,AAAOC,4BAA4B,GAAmB;QACrD,OAAO,IAAI,CAACnB,eAAe,CAACmB,4BAA4B,EAAE,CAAA;KAC1D;IAED,AAAOC,qBAAqB,GAAmB;QAC9C,OAAO,IAAI,CAACpB,eAAe,CAACoB,qBAAqB,EAAE,CAAA;KACnD;IAED,AAAOC,eAAe,GAAmB;QACxC,OAAO,IAAI,CAACrB,eAAe,CAACqB,eAAe,EAAE,CAAA;KAC7C;IAED,AAAOC,8BAA8B,GAAmB;QACvD,OAAO,IAAI,CAACtB,eAAe,CAACsB,8BAA8B,EAAE,CAAA;KAC5D;IAzHD,YAAmBC,OAAwB,CAAE;QAF7C,KAAQ1B,QAAQ,GAAsB,IAAI,AAjB3C,CAiB2C;QAGzC,IAAI,CAACG,eAAe,GAAGuB,OAAO,CAACC,UAAU,EAAE;KAC3C;CAwHD"}