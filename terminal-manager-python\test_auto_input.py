#!/usr/bin/env python3
"""
自动输入功能测试脚本
Test script for auto input functionality
"""

import time
from utils.auto_input import AutoInputManager

def test_auto_input():
    """测试自动输入功能"""
    print("=== 自动输入功能测试 ===")
    
    # 创建自动输入管理器
    auto_input = AutoInputManager()
    
    # 查找终端窗口
    print("正在查找终端窗口...")
    terminal_windows = auto_input.list_terminal_windows()
    
    if terminal_windows:
        print(f"找到 {len(terminal_windows)} 个终端窗口:")
        for i, title in enumerate(terminal_windows, 1):
            print(f"  {i}. {title}")
    else:
        print("❌ 未找到终端窗口")
        print("请先打开一个终端窗口（CMD、PowerShell或Windows Terminal）")
        return
    
    # 等待用户准备
    print("\n请确保有一个终端窗口是打开的，然后按回车继续测试...")
    input()
    
    # 测试发送命令
    test_command = "echo 'Hello from Terminal Manager!'"
    print(f"正在发送测试命令: {test_command}")
    
    success = auto_input.send_command_to_terminal(test_command)
    
    if success:
        print("✅ 命令发送成功！")
        print("请检查终端窗口是否收到并执行了命令。")
    else:
        print("❌ 命令发送失败")
    
    # 测试带路径切换的命令
    print("\n测试带路径切换的命令...")
    time.sleep(2)
    
    import os
    current_path = os.getcwd()
    test_command2 = "dir" if os.name == 'nt' else "ls -la"
    
    print(f"正在发送命令: {test_command2} (路径: {current_path})")
    success2 = auto_input.send_command_to_terminal(test_command2, current_path)
    
    if success2:
        print("✅ 带路径的命令发送成功！")
    else:
        print("❌ 带路径的命令发送失败")

if __name__ == "__main__":
    test_auto_input()
