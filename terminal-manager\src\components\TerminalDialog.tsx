import React, { useEffect, useRef } from 'react';
import { useTerminal } from '../contexts/TerminalContext';

const TerminalDialog: React.FC = () => {
  const { terminal, closeDialog, clearOutput } = useTerminal();
  const outputRef = useRef<HTMLPreElement>(null);

  useEffect(() => {
    // 自动滚动到底部
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [terminal.output]);

  if (!terminal.isDialogOpen) {
    return null;
  }

  return (
    <div className="terminal-dialog-overlay">
      <div className="terminal-dialog">
        <div className="dialog-header">
          <h3>终端输出</h3>
          <div className="dialog-controls">
            <button onClick={clearOutput} className="clear-button">
              清空
            </button>
            <button onClick={closeDialog} className="close-button">
              ✕
            </button>
          </div>
        </div>
        
        <div className="dialog-content">
          <pre ref={outputRef} className="terminal-output">
            {terminal.output || '暂无输出内容...'}
          </pre>
        </div>
        
        <div className="dialog-footer">
          <span className="output-info">
            当前路径: {terminal.currentPath || '未设置'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default TerminalDialog;
