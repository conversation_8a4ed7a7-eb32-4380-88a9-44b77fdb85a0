const { app, BrowserWindow, ipc<PERSON>ain, dialog } = require('electron');
const path = require('node:path');
const { spawn } = require('child_process');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

const createWindow = () => {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  // and load the index.html of the app.
  mainWindow.loadFile(path.join(__dirname, 'index.html'));

  // Open the DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
};

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow();

  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// IPC handlers for terminal operations
let currentTerminal = null;

// Handle folder selection
ipcMain.handle('select-folder', async () => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory']
    });

    if (result.canceled) {
      return { success: false };
    }

    return { success: true, path: result.filePaths[0] };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handle terminal startup
ipcMain.handle('start-terminal', async (event, workingPath) => {
  try {
    if (currentTerminal) {
      currentTerminal.kill();
    }

    // Start a new terminal process
    currentTerminal = spawn('cmd.exe', [], {
      cwd: workingPath,
      shell: true
    });

    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Handle command execution
ipcMain.handle('execute-command', async (event, command) => {
  return new Promise((resolve) => {
    if (!currentTerminal) {
      resolve({ output: '错误: 终端未启动', error: '终端未启动' });
      return;
    }

    let output = '';
    let errorOutput = '';

    // Create a new process for the command
    const cmdProcess = spawn('cmd.exe', ['/c', command], {
      cwd: currentTerminal.spawnargs.includes('cwd') ? currentTerminal.spawnargs.cwd : process.cwd(),
      shell: true
    });

    cmdProcess.stdout.on('data', (data) => {
      output += data.toString();
    });

    cmdProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    cmdProcess.on('close', (code) => {
      const finalOutput = output + (errorOutput ? `\n错误: ${errorOutput}` : '');
      resolve({ output: finalOutput || `命令执行完成 (退出码: ${code})` });
    });

    cmdProcess.on('error', (error) => {
      resolve({ output: `命令执行失败: ${error.message}`, error: error.message });
    });
  });
});
