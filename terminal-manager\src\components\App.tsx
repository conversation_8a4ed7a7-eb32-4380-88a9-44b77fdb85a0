import React, { useState, useEffect } from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import PathSelector from './PathSelector';
import TerminalControls from './TerminalControls';
import CommandCategories from './CommandCategories';
import TerminalDialog from './TerminalDialog';
import ThemeToggle from './ThemeToggle';
import { ThemeProvider } from '../contexts/ThemeContext';
import { TerminalProvider } from '../contexts/TerminalContext';
import { CommandProvider } from '../contexts/CommandContext';

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <TerminalProvider>
        <CommandProvider>
          <div className="app">
            <header className="app-header">
              <h1>终端管理器 v1.0</h1>
              <ThemeToggle />
            </header>
            
            <div className="app-content">
              <PanelGroup direction="vertical">
                {/* 顶部控制区域 */}
                <Panel defaultSize={15} minSize={10}>
                  <div className="control-panel">
                    <PathSelector />
                    <TerminalControls />
                  </div>
                </Panel>
                
                <PanelResizeHandle className="resize-handle horizontal" />
                
                {/* 主要内容区域 */}
                <Panel defaultSize={85} minSize={50}>
                  <PanelGroup direction="horizontal">
                    {/* 命令分类区域 */}
                    <Panel defaultSize={70} minSize={40}>
                      <CommandCategories />
                    </Panel>
                    
                    <PanelResizeHandle className="resize-handle vertical" />
                    
                    {/* 设置区域 */}
                    <Panel defaultSize={30} minSize={20}>
                      <div className="settings-panel">
                        <h3>设置</h3>
                        <p>分类管理功能将在这里实现</p>
                      </div>
                    </Panel>
                  </PanelGroup>
                </Panel>
              </PanelGroup>
            </div>
            
            <TerminalDialog />
          </div>
        </CommandProvider>
      </TerminalProvider>
    </ThemeProvider>
  );
};

export default App;
