/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS变量定义 - 亮色主题 */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #e0e0e0;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #d0d0d0;
  --accent-color: #007acc;
  --accent-hover: #005a9e;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 暗色主题 */
[data-theme="dark"] {
  --bg-primary: #1e1e1e;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3e3e3e;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #555555;
  --accent-color: #0078d4;
  --accent-hover: #106ebe;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
}

/* 应用主容器 */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.app-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.app-content {
  flex: 1;
  overflow: hidden;
}

/* 控制面板样式 */
.control-panel {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  align-items: center;
}

/* 路径选择器样式 */
.path-selector {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.path-selector label {
  font-weight: 500;
  color: var(--text-secondary);
}

.path-input-container {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 2px dashed var(--border-color);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.path-input-container.drag-over {
  border-color: var(--accent-color);
  background-color: var(--bg-tertiary);
}

.path-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.browse-button, .confirm-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.browse-button:hover, .confirm-button:hover {
  background-color: var(--bg-tertiary);
}

.drop-indicator {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  padding: 0.5rem;
  background-color: var(--accent-color);
  color: white;
  text-align: center;
  border-radius: 0 0 4px 4px;
  font-size: 0.9rem;
}

/* 终端控制样式 */
.terminal-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.start-button, .dialog-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-button {
  background-color: var(--success-color);
  color: white;
}

.start-button:hover:not(:disabled) {
  background-color: #218838;
}

.start-button:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.dialog-button {
  background-color: var(--accent-color);
  color: white;
}

.dialog-button:hover:not(:disabled) {
  background-color: var(--accent-hover);
}

.dialog-button:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--danger-color);
}

.status-dot.running {
  background-color: var(--success-color);
}

.status-text {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 主题切换按钮 */
.theme-toggle {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.2s ease;
}

.theme-toggle:hover {
  background-color: var(--bg-tertiary);
}

/* 可调整面板样式 */
.resize-handle {
  background-color: var(--border-color);
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: var(--accent-color);
}

.resize-handle.horizontal {
  height: 4px;
  cursor: row-resize;
}

.resize-handle.vertical {
  width: 4px;
  cursor: col-resize;
}

/* 命令分类样式 */
.command-categories {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1rem;
}

.category-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.category-tab {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px 4px 0 0;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-tab:hover {
  background-color: var(--bg-tertiary);
}

.category-tab.active {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.commands-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  overflow-y: auto;
}

.command-button-container {
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  transition: all 0.2s ease;
}

.command-button-container:hover {
  box-shadow: var(--shadow);
  border-color: var(--accent-color);
}

.command-info {
  flex: 1;
}

.command-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.command-text {
  display: block;
  padding: 0.5rem;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
  color: var(--text-secondary);
  word-break: break-all;
}

.command-actions {
  display: flex;
  gap: 0.5rem;
}

.command-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: var(--accent-color);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.command-button:hover:not(:disabled) {
  background-color: var(--accent-hover);
}

.command-button:disabled {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: not-allowed;
}

.confirm-actions {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.confirm-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: var(--success-color);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-button:hover:not(:disabled) {
  background-color: #218838;
}

.cancel-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: var(--danger-color);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover:not(:disabled) {
  background-color: #c82333;
}

.no-commands {
  grid-column: 1 / -1;
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
}

/* 设置面板样式 */
.settings-panel {
  padding: 1rem;
  background-color: var(--bg-secondary);
  height: 100%;
}

.settings-panel h3 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

/* 终端对话框样式 */
.terminal-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.terminal-dialog {
  width: 80%;
  height: 70%;
  max-width: 1000px;
  background-color: var(--bg-primary);
  border-radius: 8px;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: 8px 8px 0 0;
}

.dialog-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.dialog-controls {
  display: flex;
  gap: 0.5rem;
}

.clear-button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background-color: var(--bg-tertiary);
}

.close-button {
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--danger-color);
  color: white;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: #c82333;
}

.dialog-content {
  flex: 1;
  padding: 1rem;
  overflow: hidden;
}

.terminal-output {
  width: 100%;
  height: 100%;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9rem;
  padding: 1rem;
  border-radius: 4px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.dialog-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: 0 0 8px 8px;
}

.output-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}
