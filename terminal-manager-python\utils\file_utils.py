"""
文件管理工具类 - 处理文件和目录操作
File Management Utilities - Handle file and directory operations
"""

import os
import json
import shutil
import glob
from pathlib import Path
import datetime

class FileManager:
    """文件管理器类"""
    
    def __init__(self):
        """初始化文件管理器"""
        self.app_data_dir = self._get_app_data_dir()
        self._ensure_app_data_dir()
    
    def _get_app_data_dir(self):
        """获取应用数据目录"""
        if os.name == 'nt':  # Windows
            app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
            return os.path.join(app_data, 'TerminalManager')
        else:  # Unix-like systems
            return os.path.expanduser('~/.terminal-manager')
    
    def _ensure_app_data_dir(self):
        """确保应用数据目录存在"""
        os.makedirs(self.app_data_dir, exist_ok=True)
    
    def get_config_file_path(self, filename):
        """获取配置文件路径"""
        return os.path.join(self.app_data_dir, filename)
    
    def read_json_file(self, filepath, default=None):
        """
        读取JSON文件
        
        Args:
            filepath (str): 文件路径
            default: 默认值
            
        Returns:
            dict: JSON数据
        """
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"读取JSON文件失败 {filepath}: {e}")
        
        return default if default is not None else {}
    
    def write_json_file(self, filepath, data):
        """
        写入JSON文件
        
        Args:
            filepath (str): 文件路径
            data (dict): 要写入的数据
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"写入JSON文件失败 {filepath}: {e}")
            return False
    
    def read_text_file(self, filepath, encoding='utf-8'):
        """
        读取文本文件
        
        Args:
            filepath (str): 文件路径
            encoding (str): 编码格式
            
        Returns:
            str: 文件内容
        """
        try:
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding=encoding) as f:
                    return f.read()
        except Exception as e:
            print(f"读取文本文件失败 {filepath}: {e}")
        
        return ""
    
    def write_text_file(self, filepath, content, encoding='utf-8'):
        """
        写入文本文件
        
        Args:
            filepath (str): 文件路径
            content (str): 文件内容
            encoding (str): 编码格式
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"写入文本文件失败 {filepath}: {e}")
            return False
    
    def append_text_file(self, filepath, content, encoding='utf-8'):
        """
        追加文本到文件
        
        Args:
            filepath (str): 文件路径
            content (str): 要追加的内容
            encoding (str): 编码格式
            
        Returns:
            bool: 是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            with open(filepath, 'a', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"追加文本文件失败 {filepath}: {e}")
            return False
    
    def backup_file(self, filepath):
        """
        备份文件
        
        Args:
            filepath (str): 要备份的文件路径
            
        Returns:
            str: 备份文件路径，失败返回None
        """
        try:
            if not os.path.exists(filepath):
                return None
            
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{filepath}.backup_{timestamp}"
            
            shutil.copy2(filepath, backup_path)
            return backup_path
        except Exception as e:
            print(f"备份文件失败 {filepath}: {e}")
            return None
    
    def delete_file(self, filepath):
        """
        删除文件
        
        Args:
            filepath (str): 文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
            return True
        except Exception as e:
            print(f"删除文件失败 {filepath}: {e}")
            return False
    
    def create_directory(self, dirpath):
        """
        创建目录
        
        Args:
            dirpath (str): 目录路径
            
        Returns:
            bool: 是否成功
        """
        try:
            os.makedirs(dirpath, exist_ok=True)
            return True
        except Exception as e:
            print(f"创建目录失败 {dirpath}: {e}")
            return False
    
    def list_files(self, directory, pattern="*", recursive=False):
        """
        列出目录中的文件
        
        Args:
            directory (str): 目录路径
            pattern (str): 文件模式
            recursive (bool): 是否递归
            
        Returns:
            list: 文件列表
        """
        try:
            if recursive:
                return glob.glob(os.path.join(directory, "**", pattern), recursive=True)
            else:
                return glob.glob(os.path.join(directory, pattern))
        except Exception as e:
            print(f"列出文件失败 {directory}: {e}")
            return []
    
    def get_file_info(self, filepath):
        """
        获取文件信息
        
        Args:
            filepath (str): 文件路径
            
        Returns:
            dict: 文件信息
        """
        try:
            if not os.path.exists(filepath):
                return None
            
            stat = os.stat(filepath)
            return {
                "path": filepath,
                "name": os.path.basename(filepath),
                "size": stat.st_size,
                "modified": datetime.datetime.fromtimestamp(stat.st_mtime),
                "created": datetime.datetime.fromtimestamp(stat.st_ctime),
                "is_file": os.path.isfile(filepath),
                "is_dir": os.path.isdir(filepath)
            }
        except Exception as e:
            print(f"获取文件信息失败 {filepath}: {e}")
            return None
    
    def clean_old_files(self, directory, days=30, pattern="*.backup_*"):
        """
        清理旧文件
        
        Args:
            directory (str): 目录路径
            days (int): 保留天数
            pattern (str): 文件模式
            
        Returns:
            int: 删除的文件数量
        """
        try:
            cutoff_time = datetime.datetime.now() - datetime.timedelta(days=days)
            files = self.list_files(directory, pattern)
            deleted_count = 0
            
            for filepath in files:
                try:
                    stat = os.stat(filepath)
                    if datetime.datetime.fromtimestamp(stat.st_mtime) < cutoff_time:
                        os.remove(filepath)
                        deleted_count += 1
                except:
                    continue
            
            return deleted_count
        except Exception as e:
            print(f"清理旧文件失败 {directory}: {e}")
            return 0

class PathValidator:
    """路径验证器类"""
    
    @staticmethod
    def is_valid_path(path):
        """
        验证路径是否有效
        
        Args:
            path (str): 路径
            
        Returns:
            bool: 是否有效
        """
        try:
            return os.path.exists(path)
        except:
            return False
    
    @staticmethod
    def is_directory(path):
        """
        检查是否为目录
        
        Args:
            path (str): 路径
            
        Returns:
            bool: 是否为目录
        """
        try:
            return os.path.isdir(path)
        except:
            return False
    
    @staticmethod
    def is_file(path):
        """
        检查是否为文件
        
        Args:
            path (str): 路径
            
        Returns:
            bool: 是否为文件
        """
        try:
            return os.path.isfile(path)
        except:
            return False
    
    @staticmethod
    def normalize_path(path):
        """
        规范化路径
        
        Args:
            path (str): 路径
            
        Returns:
            str: 规范化后的路径
        """
        try:
            return os.path.normpath(os.path.abspath(path))
        except:
            return path
    
    @staticmethod
    def get_parent_directory(path):
        """
        获取父目录
        
        Args:
            path (str): 路径
            
        Returns:
            str: 父目录路径
        """
        try:
            return os.path.dirname(path)
        except:
            return ""
    
    @staticmethod
    def join_paths(*paths):
        """
        连接路径
        
        Args:
            *paths: 路径组件
            
        Returns:
            str: 连接后的路径
        """
        try:
            return os.path.join(*paths)
        except:
            return ""
